<!--
  Electron-WebView API 使用示例
  演示各种API功能的使用方法
-->
<template>
  <div class="electron-api-demo">
    <n-card title="Electron-WebView API 演示" class="demo-card">
      <!-- 环境检测 -->
      <n-alert v-if="!isElectronEnv" type="warning" class="mb-4">
        当前不在Electron环境中，API调用将会失败
      </n-alert>

      <n-tabs type="line" animated>
        <!-- 用户认证 -->
        <n-tab-pane name="auth" tab="用户认证">
          <div class="demo-section">
            <n-space vertical>
              <n-card title="TOKEN管理" size="small">
                <n-space vertical>
                  <n-input-group>
                    <n-input
                      v-model:value="tokenForm.accessToken"
                      placeholder="Access Token"
                      style="width: 60%"
                    />
                    <n-input
                      v-model:value="tokenForm.username"
                      placeholder="用户名"
                      style="width: 40%"
                    />
                  </n-input-group>
                  
                  <n-space>
                    <n-button
                      type="primary"
                      :loading="authLoading"
                      @click="handleSetToken"
                    >
                      设置TOKEN
                    </n-button>
                    <n-button @click="handleGetToken">获取TOKEN</n-button>
                    <n-button @click="handleClearToken">清除TOKEN</n-button>
                    <n-button @click="handleCheckTokenValidity">检查有效性</n-button>
                  </n-space>
                </n-space>
              </n-card>

              <n-card title="认证状态" size="small">
                <n-descriptions :column="2">
                  <n-descriptions-item label="认证状态">
                    <n-tag :type="isAuthenticated ? 'success' : 'error'">
                      {{ isAuthenticated ? '已认证' : '未认证' }}
                    </n-tag>
                  </n-descriptions-item>
                  <n-descriptions-item label="当前用户">
                    {{ tokenInfo?.userInfo?.name || '未知' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="TOKEN">
                    {{ tokenInfo?.accessToken ? '已设置' : '未设置' }}
                  </n-descriptions-item>
                  <n-descriptions-item label="过期时间">
                    {{ tokenInfo?.expiresAt ? new Date(tokenInfo.expiresAt).toLocaleString() : '未设置' }}
                  </n-descriptions-item>
                </n-descriptions>
              </n-card>
            </n-space>
          </div>
        </n-tab-pane>

        <!-- 流程管理 -->
        <n-tab-pane name="process" tab="流程管理">
          <div class="demo-section">
            <n-space vertical>
              <n-card title="流程详情" size="small">
                <n-space vertical>
                  <n-input
                    v-model:value="processInstanceId"
                    placeholder="请输入流程实例ID"
                  />
                  
                  <n-space>
                    <n-button
                      type="primary"
                      :loading="processLoading"
                      @click="handleOpenProcessDetail"
                    >
                      打开流程详情
                    </n-button>
                    <n-button
                      :loading="processLoading"
                      @click="handleOpenProcessInNewWindow"
                    >
                      新窗口打开
                    </n-button>
                  </n-space>
                </n-space>
              </n-card>
            </n-space>
          </div>
        </n-tab-pane>

        <!-- 导航管理 -->
        <n-tab-pane name="navigation" tab="导航管理">
          <div class="demo-section">
            <n-space vertical>
              <n-card title="模块跳转" size="small">
                <n-space vertical>
                  <n-select
                    v-model:value="selectedSystemId"
                    :options="systemOptions"
                    placeholder="选择系统"
                  />
                  
                  <n-space>
                    <n-button
                      type="primary"
                      :loading="navigationLoading"
                      @click="handleNavigateToModule"
                    >
                      跳转到模块
                    </n-button>
                    <n-button
                      :loading="navigationLoading"
                      @click="handleLoadModule"
                    >
                      仅加载路由
                    </n-button>
                  </n-space>
                </n-space>
              </n-card>

              <n-card title="菜单跳转" size="small">
                <n-space vertical>
                  <n-input-group>
                    <n-select
                      v-model:value="menuForm.systemId"
                      :options="systemOptions"
                      placeholder="选择系统"
                      style="width: 40%"
                    />
                    <n-input
                      v-model:value="menuForm.menuPath"
                      placeholder="菜单路径，如：/hrm/employee/list"
                      style="width: 60%"
                    />
                  </n-input-group>
                  
                  <n-space>
                    <n-button
                      type="primary"
                      :loading="navigationLoading"
                      @click="handleNavigateToMenu"
                    >
                      跳转到菜单
                    </n-button>
                    <n-button
                      :loading="navigationLoading"
                      @click="handleOpenMenuInNewWindow"
                    >
                      新窗口打开菜单
                    </n-button>
                  </n-space>
                </n-space>
              </n-card>
            </n-space>
          </div>
        </n-tab-pane>

        <!-- 系统状态 -->
        <n-tab-pane name="system" tab="系统状态">
          <div class="demo-section">
            <n-space vertical>
              <n-card title="系统列表" size="small">
                <n-space vertical>
                  <n-space>
                    <n-button
                      type="primary"
                      :loading="systemLoading"
                      @click="handleGetSystemList"
                    >
                      获取系统列表
                    </n-button>
                    <n-button
                      :loading="systemLoading"
                      @click="handleGetWarnNum"
                    >
                      获取警告数量
                    </n-button>
                    <n-button
                      :type="isSubscribed ? 'error' : 'success'"
                      @click="handleToggleSubscription"
                    >
                      {{ isSubscribed ? '取消订阅' : '订阅状态变化' }}
                    </n-button>
                  </n-space>
                  
                  <n-data-table
                    :columns="systemColumns"
                    :data="systems"
                    :loading="systemLoading"
                    size="small"
                  />
                </n-space>
              </n-card>

              <n-card title="状态变化日志" size="small">
                <n-scrollbar style="max-height: 200px">
                  <n-space vertical size="small">
                    <div
                      v-for="(log, index) in statusLogs"
                      :key="index"
                      class="status-log"
                    >
                      <n-tag size="small" type="info">
                        {{ new Date(log.timestamp).toLocaleTimeString() }}
                      </n-tag>
                      <span class="log-content">{{ log.message }}</span>
                    </div>
                    <div v-if="statusLogs.length === 0" class="empty-logs">
                      暂无状态变化日志
                    </div>
                  </n-space>
                </n-scrollbar>
              </n-card>
            </n-space>
          </div>
        </n-tab-pane>

        <!-- 配置和调试 -->
        <n-tab-pane name="config" tab="配置调试">
          <div class="demo-section">
            <n-space vertical>
              <n-card title="通信配置" size="small">
                <n-form :model="configForm" label-placement="left" label-width="120px">
                  <n-form-item label="超时时间(ms)">
                    <n-input-number v-model:value="configForm.timeout" :min="1000" :max="60000" />
                  </n-form-item>
                  <n-form-item label="重试次数">
                    <n-input-number v-model:value="configForm.retryCount" :min="0" :max="10" />
                  </n-form-item>
                  <n-form-item label="重试间隔(ms)">
                    <n-input-number v-model:value="configForm.retryInterval" :min="100" :max="10000" />
                  </n-form-item>
                  <n-form-item label="调试模式">
                    <n-switch v-model:value="configForm.debug" />
                  </n-form-item>
                  <n-form-item>
                    <n-button type="primary" @click="handleUpdateConfig">
                      更新配置
                    </n-button>
                  </n-form-item>
                </n-form>
              </n-card>

              <n-card title="环境信息" size="small">
                <n-descriptions :column="2">
                  <n-descriptions-item label="Electron环境">
                    <n-tag :type="isElectronEnv ? 'success' : 'error'">
                      {{ isElectronEnv ? '是' : '否' }}
                    </n-tag>
                  </n-descriptions-item>
                  <n-descriptions-item label="当前配置">
                    <n-button text @click="showCurrentConfig">查看配置</n-button>
                  </n-descriptions-item>
                </n-descriptions>
              </n-card>
            </n-space>
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { NMessage } from 'naive-ui'
import {
  ElectronBridge,
  useElectronAuth,
  useElectronSystem,
  useElectronNavigation,
  useElectronProcess,
  type SystemInfo,
  type SystemStatusChangedEvent,
} from '@/utils/electron'

// 环境检测
const isElectronEnv = computed(() => ElectronBridge.utils.isElectron())

// 用户认证相关
const {
  tokenInfo,
  isAuthenticated,
  loading: authLoading,
  setToken,
  getToken,
  clearToken,
  checkTokenValidity,
  onTokenExpired,
} = useElectronAuth()

// 系统状态相关
const {
  systems,
  warnNums,
  loading: systemLoading,
  getSystemList,
  getWarnNum,
  subscribeStatus,
  unsubscribeStatus,
} = useElectronSystem()

// 导航相关
const {
  loading: navigationLoading,
  toModule,
  toMenu,
  loadModule,
} = useElectronNavigation()

// 流程相关
const {
  loading: processLoading,
  openDetail,
  openInNewWindow,
} = useElectronProcess()

// 表单数据
const tokenForm = reactive({
  accessToken: 'demo-access-token-123456',
  username: 'admin',
})

const processInstanceId = ref('process-instance-123')

const selectedSystemId = ref<number>()

const menuForm = reactive({
  systemId: undefined as number | undefined,
  menuPath: '/hrm/employee/list',
})

const configForm = reactive({
  timeout: 10000,
  debug: false,
  retryCount: 3,
  retryInterval: 1000,
})

// 系统选项
const systemOptions = computed(() =>
  systems.value.map(system => ({
    label: system.sysName,
    value: system.id,
  }))
)

// 系统列表表格列
const systemColumns = [
  { title: 'ID', key: 'id', width: 60 },
  { title: '系统名称', key: 'sysName' },
  { title: '系统URL', key: 'sysUrl' },
  {
    title: '警告数量',
    key: 'warnNum',
    render: (row: SystemInfo) => {
      const warnNum = warnNums.value[row.id] || row.warnNum || 0
      return warnNum > 0 ? `${warnNum}` : '0'
    },
  },
]

// 状态变化日志
const statusLogs = ref<Array<{ timestamp: number; message: string }>>([])
const isSubscribed = ref(false)

// 事件处理方法
const handleSetToken = async () => {
  const result = await setToken({
    accessToken: tokenForm.accessToken,
    userInfo: {
      id: '123',
      username: tokenForm.username,
      name: `用户${tokenForm.username}`,
    },
    expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24小时后过期
  })

  if (result.success) {
    window.$message.success('TOKEN设置成功')
  } else {
    window.$message.error(`TOKEN设置失败: ${result.error}`)
  }
}

const handleGetToken = async () => {
  const result = await getToken()
  if (result.success) {
    window.$message.success('TOKEN获取成功')
  } else {
    window.$message.error(`TOKEN获取失败: ${result.error}`)
  }
}

const handleClearToken = async () => {
  const result = await clearToken()
  if (result.success) {
    window.$message.success('TOKEN清除成功')
  } else {
    window.$message.error(`TOKEN清除失败: ${result.error}`)
  }
}

const handleCheckTokenValidity = async () => {
  const isValid = await checkTokenValidity()
  window.$message.info(`TOKEN${isValid ? '有效' : '无效'}`)
}

const handleOpenProcessDetail = async () => {
  if (!processInstanceId.value) {
    window.$message.warning('请输入流程实例ID')
    return
  }

  const result = await openDetail(processInstanceId.value)
  if (result.success) {
    window.$message.success('流程详情已打开')
  } else {
    window.$message.error(`打开失败: ${result.error}`)
  }
}

const handleOpenProcessInNewWindow = async () => {
  if (!processInstanceId.value) {
    window.$message.warning('请输入流程实例ID')
    return
  }

  const result = await openInNewWindow(processInstanceId.value)
  if (result.success) {
    window.$message.success('流程详情已在新窗口打开')
  } else {
    window.$message.error(`打开失败: ${result.error}`)
  }
}

const handleNavigateToModule = async () => {
  if (!selectedSystemId.value) {
    window.$message.warning('请选择系统')
    return
  }

  const result = await toModule(selectedSystemId.value)
  if (result.success) {
    window.$message.success('模块跳转成功')
  } else {
    window.$message.error(`跳转失败: ${result.error}`)
  }
}

const handleLoadModule = async () => {
  if (!selectedSystemId.value) {
    window.$message.warning('请选择系统')
    return
  }

  const result = await loadModule(selectedSystemId.value)
  if (result.success) {
    window.$message.success('模块路由加载成功')
  } else {
    window.$message.error(`加载失败: ${result.error}`)
  }
}

const handleNavigateToMenu = async () => {
  if (!menuForm.systemId || !menuForm.menuPath) {
    window.$message.warning('请选择系统和输入菜单路径')
    return
  }

  const result = await toMenu(menuForm.systemId, menuForm.menuPath)
  if (result.success) {
    window.$message.success('菜单跳转成功')
  } else {
    window.$message.error(`跳转失败: ${result.error}`)
  }
}

const handleOpenMenuInNewWindow = async () => {
  if (!menuForm.systemId || !menuForm.menuPath) {
    window.$message.warning('请选择系统和输入菜单路径')
    return
  }

  const result = await ElectronBridge.navigation.openMenuInNewWindow(
    menuForm.systemId,
    menuForm.menuPath
  )
  if (result.success) {
    window.$message.success('菜单已在新窗口打开')
  } else {
    window.$message.error(`打开失败: ${result.error}`)
  }
}

const handleGetSystemList = async () => {
  const result = await getSystemList()
  if (result.success) {
    window.$message.success(`获取到${systems.value.length}个系统`)
  } else {
    window.$message.error(`获取失败: ${result.error}`)
  }
}

const handleGetWarnNum = async () => {
  const result = await getWarnNum()
  if (result.success) {
    window.$message.success('警告数量获取成功')
  } else {
    window.$message.error(`获取失败: ${result.error}`)
  }
}

const handleToggleSubscription = async () => {
  if (isSubscribed.value) {
    await unsubscribeStatus()
    isSubscribed.value = false
    window.$message.success('已取消订阅')
  } else {
    subscribeStatus((event: SystemStatusChangedEvent) => {
      statusLogs.value.unshift({
        timestamp: Date.now(),
        message: `系统${event.systemId}的${event.changeType}，警告数量: ${event.warnNum}`,
      })
      
      // 限制日志数量
      if (statusLogs.value.length > 50) {
        statusLogs.value = statusLogs.value.slice(0, 50)
      }
    })
    isSubscribed.value = true
    window.$message.success('已订阅状态变化')
  }
}

const handleUpdateConfig = () => {
  ElectronBridge.utils.updateConfig(configForm)
  window.$message.success('配置已更新')
}

const showCurrentConfig = () => {
  const config = ElectronBridge.utils.getConfig()
  window.$message.info(`当前配置: ${JSON.stringify(config, null, 2)}`)
}

// 监听TOKEN过期
onTokenExpired(() => {
  window.$message.warning('TOKEN已过期，请重新登录')
})

// 初始化
onMounted(async () => {
  // 获取当前配置
  const currentConfig = ElectronBridge.utils.getConfig()
  Object.assign(configForm, currentConfig)

  // 如果在Electron环境中，获取系统列表
  if (isElectronEnv.value) {
    await handleGetSystemList()
  }
})
</script>

<style scoped>
.electron-api-demo {
  padding: 20px;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  padding: 16px 0;
}

.status-log {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-content {
  font-size: 12px;
  color: #666;
}

.empty-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
