/**
 * Electron-WebView API接口
 * 提供具体的业务功能API调用方法
 */

import {
  ElectronMessageType,
  UserTokenInfo,
  SetTokenRequest,
  GetTokenResponse,
  OpenProcessDetailRequest,
  NavigateToModuleRequest,
  NavigateToMenuRequest,
  GetSystemListResponse,
  GetSystemWarnNumRequest,
  GetSystemWarnNumResponse,
  SystemStatusChangedEvent,
  ElectronApiResult,
  ElectronEventUnsubscribe,
} from './types'
import { sendElectronRequest, addElectronEventListener } from './communication'

/**
 * Electron API类
 * 封装所有与Electron主进程的通信接口
 */
export class ElectronAPI {
  // ==================== 用户认证管理 ====================

  /**
   * 设置用户TOKEN
   * @param tokenInfo 用户TOKEN信息
   */
  static async setToken(tokenInfo: UserTokenInfo): Promise<ElectronApiResult<void>> {
    const request: SetTokenRequest = { tokenInfo }
    return sendElectronRequest(ElectronMessageType.SET_TOKEN, request)
  }

  /**
   * 获取用户TOKEN
   */
  static async getToken(): Promise<ElectronApiResult<GetTokenResponse>> {
    return sendElectronRequest(ElectronMessageType.GET_TOKEN)
  }

  /**
   * 清除用户TOKEN
   */
  static async clearToken(): Promise<ElectronApiResult<void>> {
    return sendElectronRequest(ElectronMessageType.CLEAR_TOKEN)
  }

  /**
   * 监听TOKEN过期事件
   * @param callback 回调函数
   */
  static onTokenExpired(callback: () => void): ElectronEventUnsubscribe {
    return addElectronEventListener(ElectronMessageType.TOKEN_EXPIRED, callback)
  }

  // ==================== 流程详情弹窗 ====================

  /**
   * 打开流程详情弹窗
   * @param processInstanceId 流程实例ID
   * @param options 可选配置
   */
  static async openProcessDetail(
    processInstanceId: string,
    options?: {
      openInNewWindow?: boolean
      windowOptions?: {
        width?: number
        height?: number
        modal?: boolean
      }
    }
  ): Promise<ElectronApiResult<void>> {
    const request: OpenProcessDetailRequest = {
      processInstanceId,
      ...options,
    }
    return sendElectronRequest(ElectronMessageType.OPEN_PROCESS_DETAIL, request)
  }

  // ==================== 业务模块和菜单跳转 ====================

  /**
   * 跳转到业务模块
   * @param systemId 系统ID
   * @param options 可选配置
   */
  static async navigateToModule(
    systemId: number,
    options?: {
      onlyLoad?: boolean
      callbackPath?: string
    }
  ): Promise<ElectronApiResult<void>> {
    const request: NavigateToModuleRequest = {
      systemId,
      ...options,
    }
    return sendElectronRequest(ElectronMessageType.NAVIGATE_TO_MODULE, request)
  }

  /**
   * 跳转到指定菜单
   * @param systemId 系统ID
   * @param menuPath 菜单路径
   * @param options 可选配置
   */
  static async navigateToMenu(
    systemId: number,
    menuPath: string,
    options?: {
      menuParams?: Record<string, any>
      openInNewWindow?: boolean
    }
  ): Promise<ElectronApiResult<void>> {
    const request: NavigateToMenuRequest = {
      systemId,
      menuPath,
      ...options,
    }
    return sendElectronRequest(ElectronMessageType.NAVIGATE_TO_MENU, request)
  }

  // ==================== 系统状态响应式获取 ====================

  /**
   * 获取系统列表
   */
  static async getSystemList(): Promise<ElectronApiResult<GetSystemListResponse>> {
    return sendElectronRequest(ElectronMessageType.GET_SYSTEM_LIST)
  }

  /**
   * 获取系统警告数量
   * @param systemIds 系统ID列表，不传则获取所有系统
   */
  static async getSystemWarnNum(systemIds?: number[]): Promise<ElectronApiResult<GetSystemWarnNumResponse>> {
    const request: GetSystemWarnNumRequest = { systemIds }
    return sendElectronRequest(ElectronMessageType.GET_SYSTEM_WARN_NUM, request)
  }

  /**
   * 订阅系统状态变化
   * @param callback 状态变化回调函数
   */
  static subscribeSystemStatus(
    callback: (event: SystemStatusChangedEvent) => void
  ): ElectronEventUnsubscribe {
    // 先发送订阅请求
    sendElectronRequest(ElectronMessageType.SUBSCRIBE_SYSTEM_STATUS)
    
    // 监听状态变化事件
    return addElectronEventListener(ElectronMessageType.SYSTEM_STATUS_CHANGED, callback)
  }

  /**
   * 取消订阅系统状态变化
   */
  static async unsubscribeSystemStatus(): Promise<ElectronApiResult<void>> {
    return sendElectronRequest(ElectronMessageType.UNSUBSCRIBE_SYSTEM_STATUS)
  }
}

// ==================== 便捷方法 ====================

/**
 * 用户认证相关便捷方法
 */
export const ElectronAuth = {
  /**
   * 设置TOKEN
   */
  setToken: ElectronAPI.setToken,

  /**
   * 获取TOKEN
   */
  getToken: ElectronAPI.getToken,

  /**
   * 清除TOKEN
   */
  clearToken: ElectronAPI.clearToken,

  /**
   * 监听TOKEN过期
   */
  onTokenExpired: ElectronAPI.onTokenExpired,

  /**
   * 检查TOKEN是否有效
   */
  async isTokenValid(): Promise<boolean> {
    const result = await ElectronAPI.getToken()
    if (!result.success || !result.data?.tokenInfo) {
      return false
    }

    const { tokenInfo } = result.data
    if (tokenInfo.expiresAt && tokenInfo.expiresAt < Date.now()) {
      return false
    }

    return true
  },
}

/**
 * 流程相关便捷方法
 */
export const ElectronProcess = {
  /**
   * 打开流程详情
   */
  openDetail: ElectronAPI.openProcessDetail,

  /**
   * 在新窗口打开流程详情
   */
  async openDetailInNewWindow(
    processInstanceId: string,
    windowOptions?: { width?: number; height?: number }
  ): Promise<ElectronApiResult<void>> {
    return ElectronAPI.openProcessDetail(processInstanceId, {
      openInNewWindow: true,
      windowOptions,
    })
  },
}

/**
 * 导航相关便捷方法
 */
export const ElectronNavigation = {
  /**
   * 跳转到模块
   */
  toModule: ElectronAPI.navigateToModule,

  /**
   * 跳转到菜单
   */
  toMenu: ElectronAPI.navigateToMenu,

  /**
   * 仅加载模块路由（不跳转）
   */
  async loadModule(systemId: number): Promise<ElectronApiResult<void>> {
    return ElectronAPI.navigateToModule(systemId, { onlyLoad: true })
  },

  /**
   * 在新窗口打开菜单
   */
  async openMenuInNewWindow(
    systemId: number,
    menuPath: string,
    menuParams?: Record<string, any>
  ): Promise<ElectronApiResult<void>> {
    return ElectronAPI.navigateToMenu(systemId, menuPath, {
      menuParams,
      openInNewWindow: true,
    })
  },
}

/**
 * 系统状态相关便捷方法
 */
export const ElectronSystem = {
  /**
   * 获取系统列表
   */
  getList: ElectronAPI.getSystemList,

  /**
   * 获取警告数量
   */
  getWarnNum: ElectronAPI.getSystemWarnNum,

  /**
   * 订阅状态变化
   */
  subscribe: ElectronAPI.subscribeSystemStatus,

  /**
   * 取消订阅
   */
  unsubscribe: ElectronAPI.unsubscribeSystemStatus,

  /**
   * 获取单个系统的警告数量
   */
  async getSingleSystemWarnNum(systemId: number): Promise<number> {
    const result = await ElectronAPI.getSystemWarnNum([systemId])
    if (result.success && result.data?.warnNums) {
      return result.data.warnNums[systemId] || 0
    }
    return 0
  },
}


export default ElectronAPI
