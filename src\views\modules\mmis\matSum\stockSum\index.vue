<template>
  <j-crud
    :queryMethod="queryMmisMaterialSumList"
    :columns="columns"
    :queryForm="queryForm"
    name="物资汇总表"
    show-export
    :show-operation-button="true"
    :ext-table-buttons="extTableButtons"
    :paging="false"
    @query-complete="updateTotalStats"
    ref="crudRef"
    :single-line="false"
    :bordered="false"
  >
    <template #extendRightHeader>
      <n-card size="small" class="summary-card">
        <n-grid :cols="3" :x-gap="12">
          <!-- 第一行标签 -->
          <n-grid-item>
            <n-text>库存总量</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>成本金额汇总</n-text>
          </n-grid-item>
          <n-grid-item>
            <n-text>理论库存汇总</n-text>
          </n-grid-item>

          <!-- 第二行完整精度 -->
          <n-grid-item>
            <n-number-animation :from="0" :to="totalStock" />
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalCost" :precision="6">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-number-animation :from="0" :to="totalTheoretical" :precision="6" />
          </n-grid-item>

          <!-- 第三行四舍五入到2位小数 -->
          <n-grid-item></n-grid-item>
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="totalCost" :precision="2">
              <template #prefix>¥</template>
            </n-number-animation>
          </n-grid-item>
          <n-grid-item>
            <n-text>≈</n-text>
            <n-number-animation :from="0" :to="totalTheoretical" :precision="2" />
          </n-grid-item>
        </n-grid>
      </n-card>
    </template>

    <template #extendFormItems>
      <n-form-item label="物资名称">
        <n-input v-model:value="queryForm.name" :clearable="true" />
      </n-form-item>
      <n-form-item label="物资类别">
        <n-tree-select
          v-model:value="queryForm.asetType"
          :options="allAsetType"
          key-field="code"
          label-field="name"
          :clearable="true"
        ></n-tree-select>
      </n-form-item>
      <n-form-item label="仓库名称">
        <n-select
          v-model:value="queryForm.warehouseCodes"
          :options="allWrhsInfo"
          :clearable="true"
          multiple
          placeholder="请选择仓库"
          :max-tag-count="4"
        ></n-select>
      </n-form-item>
      <!--   高低值写死了，界限为2000，定制用的，要通用的话，删了就行   -->
      <n-form-item label="高低值">
        <n-select
          v-model:value="queryForm.refPriceLeval"
          value-field="value"
          :clearable="true"
          label-field="label"
          :options="[
            { label: '低值', value: '1' },
            { label: '高值', value: '2' },
          ]"
        ></n-select>
      </n-form-item>
    </template>
    <template #extendButtons>
      <n-button type="info" @click="showPrecisionConversion">精度转换测算</n-button>
      <n-button v-if="isAdmin" type="warning" @click="navigateToPrecisionAdjustment">精度调整</n-button>
    </template>
    <template #content>
      <j-modal width="100%" height="100%" title="物资查账" v-model:show="showIoRecords">
        <ioRecords :mat="rowData" />
      </j-modal>

      <j-modal width="90%" height="90%" title="精度转换汇算分析" v-model:show="showPrecisionModal">
        <PrecisionConversion :data="crudRef?.originData || []" />
      </j-modal>
    </template>
  </j-crud>
</template>
<script lang="ts" setup>
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import { h, onMounted, ref, computed, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { useUserStore } from '@/store'
  import {
    queryMmisMaterialSum,
    addMmisMaterialSum,
    deleteMmisMaterialSum,
    updateMmisMaterialSum,
    queryMmisMaterialSumList,
  } from '@/api/mmis/matSum/MaterialSumWeb'
  import { queryMmisAsetType } from '@/api/mmis/asetType/asetTypeWeb'
  import { queryAllMmisWrhsInfo } from '@/api/mmis/wrhsInfo/wrhsInfoWeb'
  import { DataTableCreateSummary, NTag, NButton, NPopover } from 'naive-ui'
  import { RowData } from 'naive-ui/es/data-table/src/interface'
  import JPGlobal from '@/types/common/jutil'
  import { number } from 'echarts'
  import ioRecords from '@/views/modules/mmis/matSum/stockSum/components/ioRecords.vue'
  import PrecisionConversion from '@/views/modules/mmis/matSum/stockSum/components/PrecisionConversion.vue'
  import { DataType } from '@/types/enums/enums'

  // data
  let queryForm = ref({ refPriceLeval: '', asetType: '', warehouseCodes: [] as string[], name: '' })
  const columns = ref<CRUDColumnInterface[]>([
    { title: '#', key: 'index', width: 50, show: false },
    { title: '库位代码', key: 'wrhsAddr', width: 100 },
    { title: '库位名称', key: 'wrhsAddrName', width: 100 },
    { title: '物资名称', key: 'name', width: 100 },
    { title: '规格', key: 'modspec', width: 100 },
    {
      title: '库存数量',
      key: 'actNum',
      width: 100,
      dataType: DataType.NUMBER,
      summary: true,
    },
    { title: '计量单位', key: 'meterUnitName', width: 100 },
    {
      title: '成本单价',
      key: 'price',
      width: 100,
      dataType: DataType.NUMBER,
    },

    {
      title: '成本金额',
      key: 'oldAmt',
      width: 100,
      dataType: DataType.NUMBER,
      summary: true,
      render: row => {
        if (row.amt !== row.oldAmt) {
          return h('div', { style: 'color: #8B0000' }, row.oldAmt)
        }
        return row.oldAmt
      },
    },
    {
      title: '理论库存',
      key: 'theoreticalNum',
      width: 100,
      dataType: DataType.NUMBER,
      summary: true,
      render: row => {
        // 理论库存数量显示
        const theoreticalContent = h('div', { style: 'font-weight: 500;' }, row.theoreticalNum || 0)

        return h(
          'div',
          {
            style: 'cursor: help;',
          },
          [
            h(
              NPopover,
              {
                trigger: 'hover',
                placement: 'top',
                width: 320,
                showArrow: true,
              },
              {
                trigger: () => theoreticalContent,
                default: () => {
                  // 辅助函数：格式化数量，去掉多余的0
                  const formatQuantity = (num: number): string => {
                    return (num || 0)
                      .toFixed(6)
                      .replace(/\.?0+$/, '')
                      .replace(/(\.\d*[1-9])0+$/, '$1')
                  }

                  return h('div', { class: 'amt-formula' }, [
                    h('div', { class: 'formula-title' }, '理论库存计算公式'),
                    h('div', { class: 'formula-item' }, [
                      h('span', { class: 'label' }, '基础库存：'),
                      h('span', { class: 'value' }, formatQuantity(row.initialNum || 0)),
                    ]),
                    h('div', { class: 'formula-item' }, [
                      h('span', { class: 'label' }, '+ 入库数量：'),
                      h('span', { class: 'value' }, formatQuantity(row.storageNum || 0)),
                    ]),
                    h('div', { class: 'formula-item' }, [
                      h('span', { class: 'label' }, '- 出库数量：'),
                      h('span', { class: 'value' }, formatQuantity(row.outboundNum || 0)),
                    ]),
                    h('div', { class: 'formula-line' }),
                    h('div', { class: 'formula-item formula-result' }, [
                      h('span', { class: 'label' }, '= 理论库存：'),
                      h('span', { class: 'value' }, formatQuantity(row.theoreticalNum || 0)),
                    ]),
                  ])
                },
              }
            ),
          ]
        )
      },
    },
  ])
  let rowData = ref({})
  let showIoRecords = ref(false)
  let showPrecisionModal = ref(false)
  const router = useRouter()
  const userStore = useUserStore()

  // 判断当前用户是否为管理员
  const isAdmin = computed(() => {
    return userStore.getUserInfo?.username === 'admin'
  })

  //所有物资目录(树形数据)
  let allAsetType = ref<Array<any>>([])
  //查询所有物资目录
  const queryAllAsetType = () => {
    queryMmisAsetType({}).then(res => {
      allAsetType.value = res.data
    })
  }

  let allWrhsInfo = ref<Array<any>>([])
  const queryAllWrhsInfo = () => {
    queryAllMmisWrhsInfo({}).then(res => {
      res.data.forEach((item: any) => {
        allWrhsInfo.value.push({
          label: item.wrhsName,
          value: item.wrhsCode,
        })
      })
    })
  }
  onMounted(() => {
    queryAllAsetType()
    queryAllWrhsInfo()
  })

  //汇总
  const createSummary = (data: any) => {
    let res: any = {}
    let ct = 0
    for (let column of columns.value) {
      if (ct == 0) {
        res[column.key] = {
          value: '汇总',
        }
      } else {
        let sum = 0
        if (column.summary) {
          data.forEach((d: any) => (sum += d[column.key]))
          res[column.key] = {
            value: h('p', { innerHTML: JPGlobal.formatCost(sum, true) }),
          }
        } else {
          res[column.key] = {
            value: '',
          }
        }
      }
      ct++
    }
    return res
  }
  let crudRef = ref()
  let totalStock = ref(0)
  let totalCost = ref(0)
  let totalTheoretical = ref(0)

  const updateTotalStats = () => {
    if (!crudRef.value?.originData) return

    totalStock.value = 0
    totalCost.value = 0
    totalTheoretical.value = 0

    crudRef.value.originData.forEach((item: any) => {
      totalStock.value += item.actNum || 0
      totalCost.value += item.oldAmt || 0
      totalTheoretical.value += item.theoreticalNum || 0  // 改为理论库存数量
    })
  }

  watch(
    () => crudRef.value?.originData,
    () => {
      updateTotalStats()
    },
    { deep: true }
  )

  const summaryCustom = (data: any) => {
    if (!crudRef.value?.originData || crudRef.value.originData.length === 0) return

    let res: any = {}
    let ct = 0
    for (let column of columns.value) {
      if (ct == 0) {
        res[column.key] = {
          value: '汇总',
        }
      } else {
        let sum = 0
        if (column.summary) {
          crudRef.value.originData.forEach((d: any) => (sum += d[column.key]))
          // 对于理论库存列，不使用金额格式化
          if (column.key === 'theoreticalNum') {
            res[column.key] = {
              value: h('p', { innerHTML: sum.toFixed(6).replace(/\.?0+$/, '') }),
            }
          } else {
            res[column.key] = {
              value: h('p', { innerHTML: JPGlobal.formatCost(sum, true) }),
            }
          }
        } else {
          res[column.key] = {
            value: '',
          }
        }
      }
      ct++
    }
    crudRef.value.summary = res
  }

  // 显示精度转换汇算弹窗
  const showPrecisionConversion = () => {
    if (!crudRef.value?.originData || crudRef.value.originData.length === 0) {
      window.$message.warning('请先查询数据')
      return
    }
    showPrecisionModal.value = true
  }

  // 跳转到精度调整页面
  const navigateToPrecisionAdjustment = () => {
    router.push('/mmis/matSum/stockSum/precisionAdjustment')
  }

  const extTableButtons = computed(() => {
    return [
      h(
        NButton,
        {
          type: 'success',
          strong: true,
          secondary: true,
          size: 'small',
          callback: (row: any) => {
            // 确保传递必要的字段，特别是唯一编码
            if (!row.matUniqueCode) {
              window.$message.warning('缺少物资唯一编码，无法查看详细记录')
              return
            }
            
            showIoRecords.value = true
            rowData.value = row
            console.log('当前选中的物资数据:', {
              matUniqueCode: row.matUniqueCode,
              itemNum: row.itemNum,
              name: row.name,
              wrhsAddr: row.wrhsAddr
            })
          },
          style: {
            cursor: 'pointer',
          },
        },
        () => '📋 物资查账'
      ),
      // 如果需要添加更多按钮，可以在这里继续添加
      // h(
      //   NButton,
      //   {
      //     type: 'info',
      //     strong: true,
      //     secondary: true,
      //     size: 'small',
      //     callback: (row: any) => {
      //       // 其他功能按钮
      //     },
      //   },
      //   () => '🔍 其他功能'
      // ),
    ]
  })
</script>

<script lang="ts">
  export default {
    name: 'index',
  }
</script>

<style lang="scss" scoped>
  .summary-card {
    margin-bottom: 16px;
  }

  .amt-formula {
    padding: 8px;

    .formula-title {
      font-weight: bold;
      margin-bottom: 12px;
      text-align: center;
      font-size: 15px;
      color: #303133;
    }

    .formula-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .label {
        color: #606266;
        font-size: 14px;
      }

      .value {
        font-weight: 500;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 14px;
      }
    }

    .formula-line {
      height: 1px;
      background-color: #dcdfe6;
      margin: 12px 0;
    }

    .formula-result {
      font-weight: bold;

      .label {
        font-size: 15px;
        color: #303133;
      }

      .value {
        color: #8b0000;
        font-size: 15px;
      }
    }
  }
</style>
