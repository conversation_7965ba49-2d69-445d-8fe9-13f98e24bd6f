# AmsPropertyReadMapper queryList0 方法模块化重构

## 任务背景
重构 `sfm_back/med-ams/src/main/resources/mapper/amsProperty/read/AmsPropertyReadMapper.xml` 中的 `queryList0` 方法，将复杂的 SQL 查询拆分为结构化的模块片段。

## 重构目标
- 提高代码可读性和维护性
- 将复杂查询拆分为功能明确的片段
- 保持原有功能完全等价
- 添加清晰的中文注释

## 执行计划

### 1. 创建模块化 SQL 片段
- [ ] 字段选择片段 (`selectFieldsForQueryList0`)
- [ ] 表连接片段 (`joinTablesForQueryList0`)
- [ ] 权限过滤片段 (`permissionFilterForQueryList0`)
- [ ] 基础条件片段 (`basicConditionsForQueryList0`)
- [ ] 部门过滤片段 (`deptFilterForQueryList0`)
- [ ] 扩展表单片段 (`extendFormConditionsForQueryList0`)
- [ ] 排序片段 (`orderByForQueryList0`)

### 2. 重构主查询方法
- [ ] 使用 `<include>` 引用各个片段
- [ ] 保持原有条件判断逻辑
- [ ] 验证功能等价性

## 重构成果

### 原始方法问题
- 单个方法超过400行，可读性差
- 复杂的嵌套逻辑难以维护
- 权限过滤、部门查询等逻辑混杂
- 缺乏清晰的功能分层

### 重构后优势
- **模块化设计**：7个功能明确的SQL片段
- **可读性提升**：每个片段职责单一，逻辑清晰
- **维护性增强**：修改某个功能只需关注对应片段
- **复用性强**：片段可在其他查询中复用
- **调试友好**：问题定位更精确

### 片段说明
1. `selectFieldsForQueryList0` - 字段选择逻辑
2. `joinTablesForQueryList0` - 表连接逻辑
3. `permissionFilterForQueryList0` - 权限过滤逻辑
4. `basicConditionsForQueryList0` - 基础条件过滤
5. `deptFilterForQueryList0` - 部门过滤逻辑
6. `extendFormConditionsForQueryList0` - 扩展表单条件
7. `orderByForQueryList0` - 排序逻辑

## 当前状态
✅ **重构完成** - 功能等价，结构优化

## 文件位置
`sfm_back/med-ams/src/main/resources/mapper/amsProperty/read/AmsPropertyReadMapper.xml`
