/**
 * WebView端消息处理器
 * 处理来自Electron主进程的消息，并与现有Vue应用集成
 */

import { useEventBus } from '@/utils/eventBus'
import { useSysStore, useTempStore } from '@/store'
import { addDynamicRoute } from '@/router'
import { querySystemConfig } from '@/api/sys/system'
import {
  ElectronMessageType,
  ElectronRequestMessage,
  ElectronResponseMessage,
  OpenProcessDetailRequest,
  NavigateToModuleRequest,
  NavigateToMenuRequest,
  GetSystemWarnNumRequest,
  SystemInfo,
  ElectronErrorCode,
} from './types'

/**
 * WebView消息处理器类
 */
export class WebViewMessageHandler {
  private eventBus = useEventBus()
  private sysStore = useSysStore()
  private tempStore = useTempStore()

  constructor() {
    this.initializeMessageListener()
  }

  /**
   * 初始化消息监听器
   */
  private initializeMessageListener(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleMessage.bind(this))
    }
  }

  /**
   * 处理接收到的消息
   */
  private async handleMessage(event: MessageEvent): Promise<void> {
    try {
      const message = event.data as ElectronRequestMessage

      if (!message || !message.type || !message.id || message.source !== 'electron') {
        return
      }

      console.log('[WebViewHandler] 收到Electron消息:', message)

      let response: ElectronResponseMessage

      try {
        const result = await this.processMessage(message)
        response = {
          id: message.id,
          type: ElectronMessageType.RESPONSE,
          timestamp: Date.now(),
          source: 'webview',
          success: true,
          data: result,
        }
      } catch (error) {
        console.error('[WebViewHandler] 处理消息失败:', error)
        response = {
          id: message.id,
          type: ElectronMessageType.ERROR,
          timestamp: Date.now(),
          source: 'webview',
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          errorCode: ElectronErrorCode.SYSTEM_ERROR,
        }
      }

      // 发送响应
      this.sendResponse(response)
    } catch (error) {
      console.error('[WebViewHandler] 消息处理异常:', error)
    }
  }

  /**
   * 处理具体的消息类型
   */
  private async processMessage(message: ElectronRequestMessage): Promise<any> {
    switch (message.type) {
      case ElectronMessageType.SET_TOKEN:
        return this.handleSetToken(message.data)

      case ElectronMessageType.GET_TOKEN:
        return this.handleGetToken()

      case ElectronMessageType.CLEAR_TOKEN:
        return this.handleClearToken()

      case ElectronMessageType.OPEN_PROCESS_DETAIL:
        return this.handleOpenProcessDetail(message.data)

      case ElectronMessageType.NAVIGATE_TO_MODULE:
        return this.handleNavigateToModule(message.data)

      case ElectronMessageType.NAVIGATE_TO_MENU:
        return this.handleNavigateToMenu(message.data)

      case ElectronMessageType.GET_SYSTEM_LIST:
        return this.handleGetSystemList()

      case ElectronMessageType.GET_SYSTEM_WARN_NUM:
        return this.handleGetSystemWarnNum(message.data)

      case ElectronMessageType.SUBSCRIBE_SYSTEM_STATUS:
        return this.handleSubscribeSystemStatus()

      case ElectronMessageType.UNSUBSCRIBE_SYSTEM_STATUS:
        return this.handleUnsubscribeSystemStatus()

      default:
        throw new Error(`不支持的消息类型: ${message.type}`)
    }
  }

  /**
   * 发送响应消息
   */
  private sendResponse(response: ElectronResponseMessage): void {
    if (window.parent !== window) {
      window.parent.postMessage(response, '*')
    } else if ((window as any).electronAPI?.postMessage) {
      (window as any).electronAPI.postMessage(response)
    }
  }

  // ==================== 消息处理方法 ====================

  /**
   * 处理设置TOKEN
   */
  private async handleSetToken(data: any): Promise<void> {
    const { tokenInfo } = data
    
    // 这里可以将TOKEN保存到localStorage或其他存储中
    if (tokenInfo.accessToken) {
      localStorage.setItem('access_token', tokenInfo.accessToken)
    }
    if (tokenInfo.refreshToken) {
      localStorage.setItem('refresh_token', tokenInfo.refreshToken)
    }
    if (tokenInfo.userInfo) {
      localStorage.setItem('user_info', JSON.stringify(tokenInfo.userInfo))
    }

    console.log('[WebViewHandler] TOKEN已设置')
  }

  /**
   * 处理获取TOKEN
   */
  private async handleGetToken(): Promise<any> {
    const accessToken = localStorage.getItem('access_token')
    const refreshToken = localStorage.getItem('refresh_token')
    const userInfoStr = localStorage.getItem('user_info')

    let userInfo = null
    if (userInfoStr) {
      try {
        userInfo = JSON.parse(userInfoStr)
      } catch (error) {
        console.warn('[WebViewHandler] 解析用户信息失败:', error)
      }
    }

    const tokenInfo = accessToken ? {
      accessToken,
      refreshToken,
      userInfo,
    } : null

    return { tokenInfo }
  }

  /**
   * 处理清除TOKEN
   */
  private async handleClearToken(): Promise<void> {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
    
    console.log('[WebViewHandler] TOKEN已清除')
  }

  /**
   * 处理打开流程详情
   */
  private async handleOpenProcessDetail(data: OpenProcessDetailRequest): Promise<void> {
    const { processInstanceId } = data

    // 使用现有的事件总线机制触发流程详情弹窗
    this.eventBus.emit('open-process-detail', {
      processInstanceId,
    })

    console.log('[WebViewHandler] 已触发流程详情弹窗:', processInstanceId)
  }

  /**
   * 处理跳转到业务模块
   */
  private async handleNavigateToModule(data: NavigateToModuleRequest): Promise<void> {
    const { systemId, onlyLoad = false, callbackPath } = data

    try {
      // 获取系统信息
      const res = await querySystemConfig({ id: systemId })
      const systemInfo = res.data[0]

      if (!systemInfo) {
        throw new Error(`未找到系统ID为 ${systemId} 的系统`)
      }

      // 保存历史系统信息
      this.tempStore.setHisSystemInfo(this.sysStore.getSystemInfo)

      // 设置新的系统信息
      this.sysStore.setSystemInfo({
        systemId: systemInfo.id,
        systemName: systemInfo.sysName,
        sysUrl: systemInfo.sysUrl,
      })

      // 加载动态路由
      await addDynamicRoute(systemId)

      if (!onlyLoad) {
        // 跳转到指定路径或默认工作台
        const targetPath = callbackPath || '/home'
        
        // 这里需要获取router实例进行跳转
        // 由于在工具函数中，需要通过其他方式获取router
        const router = (await import('vue-router')).useRouter()
        router.push({ path: targetPath })
      }

      console.log('[WebViewHandler] 已跳转到业务模块:', systemInfo.sysName)
    } catch (error) {
      console.error('[WebViewHandler] 跳转业务模块失败:', error)
      throw error
    }
  }

  /**
   * 处理跳转到菜单
   */
  private async handleNavigateToMenu(data: NavigateToMenuRequest): Promise<void> {
    const { systemId, menuPath, menuParams } = data

    try {
      // 先跳转到业务模块
      await this.handleNavigateToModule({ systemId, onlyLoad: false })

      // 然后跳转到具体菜单
      const router = (await import('vue-router')).useRouter()
      router.push({
        path: menuPath,
        query: menuParams,
      })

      console.log('[WebViewHandler] 已跳转到菜单:', menuPath)
    } catch (error) {
      console.error('[WebViewHandler] 跳转菜单失败:', error)
      throw error
    }
  }

  /**
   * 处理获取系统列表
   */
  private async handleGetSystemList(): Promise<any> {
    try {
      const res = await querySystemConfig({})
      const systems: SystemInfo[] = res.data || []

      // 获取系统警告数量（这里可以调用实际的API获取）
      // 暂时返回模拟数据，实际项目中应该调用相应的API
      const systemWarnCounts = this.getMockSystemWarnCounts()

      // 合并警告数量到系统信息中
      const systemsWithWarnNum = systems.map(system => ({
        ...system,
        warnNum: systemWarnCounts[system.id] || 0,
      }))

      return { systems: systemsWithWarnNum }
    } catch (error) {
      console.error('[WebViewHandler] 获取系统列表失败:', error)
      throw error
    }
  }

  /**
   * 处理获取系统警告数量
   */
  private async handleGetSystemWarnNum(data: GetSystemWarnNumRequest): Promise<any> {
    try {
      const { systemIds } = data

      // 获取系统警告数量（这里可以调用实际的API获取）
      const systemWarnCounts = this.getMockSystemWarnCounts()

      // 如果指定了系统ID列表，则只返回这些系统的警告数量
      const warnNums = systemIds
        ? systemIds.reduce((acc, id) => {
            acc[id] = systemWarnCounts[id] || 0
            return acc
          }, {} as Record<number, number>)
        : systemWarnCounts

      return { warnNums }
    } catch (error) {
      console.error('[WebViewHandler] 获取系统警告数量失败:', error)
      throw error
    }
  }

  /**
   * 处理订阅系统状态
   */
  private async handleSubscribeSystemStatus(): Promise<void> {
    // 这里可以设置定时器或监听器来监控系统状态变化
    // 当状态发生变化时，发送事件通知给Electron主进程
    console.log('[WebViewHandler] 已订阅系统状态变化')
  }

  /**
   * 处理取消订阅系统状态
   */
  private async handleUnsubscribeSystemStatus(): Promise<void> {
    // 清理定时器或监听器
    console.log('[WebViewHandler] 已取消订阅系统状态变化')
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取模拟的系统警告数量
   * 实际项目中应该调用相应的API获取真实数据
   */
  private getMockSystemWarnCounts(): Record<number, number> {
    return {
      1: 5,   // 核心系统
      2: 2,   // 人力资源
      15: 8,  // 费用报销
      18: 12, // 业务审批
    }
  }

  /**
   * 发送事件通知给Electron主进程
   */
  public sendEventNotification(type: ElectronMessageType, data: any): void {
    const message = {
      id: `event-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      type,
      timestamp: Date.now(),
      source: 'webview' as const,
      data,
    }

    this.sendResponse(message as any)
  }
}

// 创建全局实例
export const webViewHandler = new WebViewMessageHandler()

// 导出便捷方法
export const sendEventToElectron = webViewHandler.sendEventNotification.bind(webViewHandler)
