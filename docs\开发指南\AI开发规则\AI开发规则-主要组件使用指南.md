# 🚀 AI开发规则 - 主要组件使用指南

## 📋 概述

本项目有三个核心页面组件模板，用于快速构建页面并自动处理高度计算，减少重复代码：

1. **工作台页面模板** - 用于仪表板、首页等展示型页面（无查询表单）
2. **普通Container页面模板** - 用于有查询表单但无标准CRUD操作的页面
3. **CRUD页面模板** - 用于数据管理、表格操作等功能型页面

## 🏠 1. 工作台页面模板 （可以使用tsx以及tailwindcss但是需要与naiveui默认主题保持一致性）

### 1.1 基础模板结构

```vue
<template>
  <j-container :showHeader="false" @contentHeight="val => (contentHeight = val)">
    <template #content>
      <!-- 页面内容区域 -->
      <div class="dashboard-container" :style="{ height: contentHeight, padding: '16px' }">
        <!-- 你的页面内容 -->
      </div>
    </template>
  </j-container>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

// 自动获取内容区域高度
const contentHeight = ref<string>('')
</script>

<style lang="less" scoped>
.dashboard-container {
  overflow-y: auto;
  background-color: #f5f7fa;
}
</style>
```

### 1.2 使用规则

#### ✅ 必须遵循的规则

1. **高度管理**：
   - 必须使用 `@contentHeight="val => (contentHeight = val)"` 监听高度变化
   - 容器元素必须设置 `:style="{ height: contentHeight }"`
   - 根据需要添加 `overflow-y: auto` 实现滚动

2. **容器配置**：
   - 工作台页面通常设置 `:showHeader="false"` 隐藏查询表单区域
   - 使用 `#content` 插槽放置页面内容

3. **样式规范**：
   - 推荐使用 `padding: '16px'` 作为内边距
   - 背景色推荐使用 `#f5f7fa` 保持一致性

#### 🎯 适用场景

- 仪表板页面
- 数据可视化页面
- 首页/工作台
- 统计报表页面
- 无需查询表单的展示页面

### 1.3 完整示例

```vue
<template>
  <j-container :showHeader="false" @contentHeight="val => (contentHeight = val)">
    <template #content>
      <div class="pms-dashboard" :style="{ height: contentHeight, padding: '16px' }">
        <!-- 顶部统计卡片 -->
        <n-grid :cols="4" :x-gap="16" :y-gap="16" class="stats-section">
          <n-grid-item v-for="stat in statsData" :key="stat.key">
            <div class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <n-icon :size="32" :color="stat.iconColor">
                    <component :is="stat.icon" />
                  </n-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-title">{{ stat.title }}</div>
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-desc">{{ stat.desc }}</div>
                </div>
              </div>
            </div>
          </n-grid-item>
        </n-grid>

        <!-- 主要内容区 -->
        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="main-content">
          <!-- 你的内容区域 -->
        </n-grid>
      </div>
    </template>
  </j-container>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const contentHeight = ref<string>('')

// 统计数据
const statsData = ref([
  {
    key: 'totalDepts',
    title: '科室总数',
    value: '156',
    desc: '已配置科室',
    icon: 'PieChartOutline',
    iconColor: '#409EFF'
  }
  // ... 更多数据
])
</script>

<style lang="less" scoped>
.pms-dashboard {
  overflow-y: auto;
  background-color: #f5f7fa;

  .stats-section {
    margin-bottom: 20px;
  }

  .stat-card {
    height: 100px;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    background: #fff;

    .stat-content {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 16px;
    }
  }
}
</style>
```

## 📋 2. 普通Container页面模板

### 2.1 基础模板结构

```vue
<template>
  <j-container
    @contentHeight="handleContentHeight"
    :queryForm="queryParams"
    @query="queryData"
  >
    <!-- 扩展查询表单项 -->
    <template #extendFormItems>
      <n-form-item label="计算月份">
        <n-date-picker
          v-model:formatted-value="queryParams.calcMonth"
          type="month"
          :format="'yyyy-MM'"
          @update:formatted-value="updateQueryParams"
        />
      </n-form-item>
      <n-form-item label="状态">
        <n-select
          v-model:value="queryParams.status"
          :options="statusOptions"
          clearable
        />
      </n-form-item>
    </template>

    <!-- 扩展操作按钮 -->
    <template #extendButtons>
      <j-export
        :exportConfig="exportConfig"
        :show-exportExcel-template="!disableToCalc"
      />
      <j-upload
        v-if="!disableToCalc"
        accept-prop=".xlsx"
        load-title-name="上传Excel"
        @afterUpload="uploadExcel"
        :max-amount="1"
      />
      <n-button v-if="!disableToCalc" @click="handleCalculate">
        计算
      </n-button>
      <n-button v-if="!disableToCalc" @click="saveData">
        保存
      </n-button>
    </template>

    <!-- 内容区域 -->
    <template #content>
      <Detail
        ref="detailRef"
        :queryParams="queryParams"
        :contentHeight="contentHeight"
        :disableToEdit="disableToCalc"
      />
    </template>
  </j-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Detail from './comp/detail.vue'
import { getDefaultMonth } from './types/performance'
import { checkCalcResultIsDisable } from '@/api/pms/clac/AwardCalcAuditWeb'
import { ExcelImportUtil } from '@/utils/excelImportUtil'
import { ExcelType } from '@/utils/excel'

// 组件引用
const detailRef = ref(null)
const contentHeight = ref(0)
const disableToCalc = ref(false)

// 查询参数
const queryParams = ref({
  calcMonth: getDefaultMonth(),
  status: '',
  // 其他查询参数...
})

// 导出配置
const exportConfig = computed<ExcelType>(() => {
  return {
    tableRef: detailRef.value?.currentTableRef,
    excelName: detailRef.value?.currentExcelName,
  }
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '进行中', value: '1' },
  { label: '已完成', value: '2' }
]

// 处理内容高度变化
const handleContentHeight = (height: string) => {
  contentHeight.value = parseInt(height.replace(/[^\d.-]/g, ''), 10) + 90
}

// 更新查询参数
const updateQueryParams = (calcMonth: string) => {
  queryParams.value.calcMonth = calcMonth
  queryData()
}

// 查询数据
const queryData = async () => {
  try {
    // 检查计算结果是否禁用
    const res = await checkCalcResultIsDisable(queryParams.value)
    disableToCalc.value = res.data

    // 调用子组件查询方法
    detailRef.value?.queryData(queryParams.value)
  } catch (error) {
    console.error('查询失败:', error)
  }
}

// 保存数据
const saveData = () => {
  detailRef.value?.saveData(queryParams.value)
}

// 处理计算按钮点击
const handleCalculate = () => {
  detailRef.value?.handleCalculate(queryParams.value)
}

// 导入Excel
const uploadExcel = (files: Array<File>) => {
  ExcelImportUtil.readExcelFile(files[0]).then(res => {
    if (!res.success) {
      window.$message.error(res.error)
      return
    }
    detailRef.value?.handleUploadExcel(res.data)
  })
}
</script>
```

### 2.2 使用规则

#### ✅ 必须遵循的规则

1. **查询表单配置**：
   - 必须提供 `:queryForm="queryParams"` 绑定查询参数
   - 监听 `@query="queryData"` 事件处理查询逻辑
   - 查询参数对象使用 `ref` 创建响应式数据

2. **高度管理**：
   - 使用 `@contentHeight="handleContentHeight"` 监听高度变化
   - 根据需要调整内容区域高度计算逻辑
   - 传递给子组件使用

3. **插槽使用**：
   - `#extendFormItems`: 添加自定义查询表单项
   - `#extendButtons`: 添加自定义操作按钮
   - `#content`: 放置主要内容区域

4. **子组件通信**：
   - 使用 `ref` 获取子组件实例
   - 通过子组件方法调用实现数据操作
   - 传递查询参数和状态给子组件

#### 🎯 适用场景

- 需要查询条件但不需要完整CRUD功能的页面
- 数据计算和处理页面
- 报表查询和展示页面
- 数据导入导出页面
- 需要自定义操作按钮的页面

### 2.3 与工作台模板的区别

| 特性 | 工作台模板 | 普通Container模板 |
|------|------------|-------------------|
| 查询表单 | ❌ 无 (`showHeader: false`) | ✅ 有查询表单 |
| 查询按钮 | ❌ 无 | ✅ 有查询按钮 |
| 自定义按钮 | ❌ 无 | ✅ 支持扩展按钮 |
| 适用场景 | 仪表板、首页 | 查询页面、处理页面 |
| 高度处理 | 简单传递 | 可能需要额外计算 |

## 📊 3. CRUD页面模板

### 3.1 基础模板结构

```vue
<template>
  <j-crud
    ref="crudRef"
    :queryMethod="queryData"
    :addMethod="addData"
    :updateMethod="updateData"
    :delMethod="deleteData"
    :columns="columns"
    :queryForm="queryForm"
    name="数据管理"
    showAddButton
  >
    <!-- 扩展查询表单项 -->
    <template #extendFormItems>
      <n-form-item label="名称">
        <n-input v-model:value="queryForm.name" clearable placeholder="请输入名称" />
      </n-form-item>
      <n-form-item label="状态">
        <n-select v-model:value="queryForm.status" :options="statusOptions" clearable />
      </n-form-item>
    </template>

    <!-- 扩展操作按钮 -->
    <template #extendButtons>
      <n-button type="primary" @click="handleCustomAction">
        自定义操作
      </n-button>
    </template>
  </j-crud>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import type { CRUDColumn } from '@/components/common/crud/types'

// CRUD组件引用
const crudRef = ref()

// 查询表单
const queryForm = reactive({
  name: '',
  status: '',
  pageNum: 1,
  pageSize: 20
})

// 表格列配置
const columns = ref<CRUDColumn[]>([
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '名称',
    key: 'name',
    show: true,
    required: true,
    type: 'input',
    placeholder: '请输入名称'
  },
  {
    title: '状态',
    key: 'status',
    show: true,
    type: 'select',
    selection: statusOptions,
    tagRender: true
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180,
    align: 'center'
  }
])

// 状态选项
const statusOptions = [
  { label: '启用', value: '1' },
  { label: '禁用', value: '0' }
]

// API方法
const queryData = async (params: any) => {
  // 查询数据的API调用
  return await api.query(params)
}

const addData = async (data: any) => {
  // 新增数据的API调用
  return await api.add(data)
}

const updateData = async (data: any) => {
  // 更新数据的API调用
  return await api.update(data)
}

const deleteData = async (ids: string[]) => {
  // 删除数据的API调用
  return await api.delete(ids)
}

// 自定义操作
const handleCustomAction = () => {
  console.log('执行自定义操作')
}
</script>
```

### 3.2 使用规则

#### ✅ 必须遵循的规则

1. **API方法配置**：
   - 至少提供 `queryMethod` 用于数据查询
   - 如需完整CRUD功能，提供 `addMethod`、`updateMethod`、`delMethod`
   - API方法必须返回Promise，响应格式要符合组件要求

2. **列配置规范**：
   - 每列必须有唯一的 `key` 值
   - 表单字段设置 `show: true` 在新增/编辑表单中显示
   - 必填字段设置 `required: true`
   - 合理设置列宽度 `width` 避免布局问题

3. **查询表单**：
   - 必须包含 `pageNum` 和 `pageSize` 字段用于分页
   - 使用 `reactive` 创建响应式查询表单对象

4. **组件引用**：
   - 使用 `ref="crudRef"` 获取组件实例，便于调用组件方法

#### 🎯 适用场景

- 数据管理页面
- 表格展示与操作
- 需要查询、新增、编辑、删除功能的页面
- 带分页的列表页面

### 3.3 高级配置示例

```vue
<template>
  <j-crud
    ref="crudRef"
    :queryMethod="queryData"
    :addMethod="addData"
    :updateMethod="updateData"
    :delMethod="deleteData"
    :columns="columns"
    :queryForm="queryForm"
    :tabs="tabs"
    :defaultCheckTab="'1'"
    name="项目管理"
    showAddButton
    :showExport="true"
    :exportConfig="exportConfig"
    :showOperationButton="false"
    @queryComplete="handleQueryComplete"
    @add="handleAdd"
    @edit="handleEdit"
  >
    <template #extendFormItems>
      <n-form-item label="项目名称">
        <n-input v-model:value="queryForm.projectName" clearable />
      </n-form-item>
      <n-form-item label="申报时间">
        <n-date-picker
          v-model:formatted-value="createTimeRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          clearable
        />
      </n-form-item>
    </template>
  </j-crud>
</template>
```

## 🔧 4. 通用开发规则

### 4.1 命名规范

- 页面文件：`index.vue`
- 组件引用：`crudRef`、`containerRef`
- 高度变量：`contentHeight`
- 查询表单：`queryForm`
- 表格列：`columns`

### 4.2 代码组织

```typescript
// 1. 导入依赖
import { ref, reactive } from 'vue'
import type { CRUDColumn } from '@/components/common/crud/types'

// 2. 组件引用
const crudRef = ref()
const contentHeight = ref<string>('')

// 3. 响应式数据
const queryForm = reactive({...})
const columns = ref<CRUDColumn[]>([...])

// 4. API方法
const queryData = async (params: any) => {...}
const addData = async (data: any) => {...}

// 5. 事件处理方法
const handleAdd = () => {...}
const handleEdit = () => {...}
```

### 4.3 样式规范

```scss
// 工作台页面样式
.dashboard-container {
  overflow-y: auto;
  background-color: #f5f7fa;

  .section-card {
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    background: #fff;
  }
}

// CRUD页面通常不需要额外样式，组件内部已处理
```

## ⚠️ 5. 注意事项

1. **高度计算**：三个模板都会自动处理高度计算，无需手动干预
2. **API响应格式**：确保API返回格式符合组件要求
3. **权限控制**：CRUD组件会自动检查按钮权限
4. **性能优化**：大数据量时组件会自动启用虚拟滚动
5. **错误处理**：组件内部已处理常见错误情况

## 📚 6. j-container 组件详细配置

### 6.1 核心Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showHeader` | Boolean | `true` | 是否显示头部查询区域 |
| `leftSideMode` | Boolean | `false` | 是否启用左侧边栏模式 |
| `leftSideWidth` | String | `'20%'` | 左侧边栏宽度 |
| `queryForm` | Object | `{}` | 查询表单数据对象 |
| `pagination` | Object | `{}` | 分页配置 |
| `showExport` | Boolean | `false` | 是否显示导出按钮 |
| `labelWidth` | String | `'auto'` | 表单标签宽度 |

### 6.2 重要事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `contentHeight` | `(heightString)` | 内容区域高度变化时触发 |
| `query` | `()` | 点击查询按钮时触发 |
| `pageSizeUpdate` | `(pageSize)` | 分页大小变化时触发 |
| `pageNumUpdate` | `(pageNum)` | 页码变化时触发 |

### 6.3 插槽说明

| 插槽名 | 说明 | 使用场景 |
|--------|------|----------|
| `#content` | 主要内容区域 | 放置表格、图表等主要内容 |
| `#leftSide` | 左侧边栏内容 | 树形菜单、导航等 |
| `#extendFormItems` | 扩展查询表单项 | 添加自定义查询条件 |
| `#extendButtons` | 扩展操作按钮 | 添加自定义操作按钮 |
| `#contentTop` | 内容区顶部 | Tab页签、工具栏等 |

## 📊 7. j-crud 组件详细配置

### 7.1 核心Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `queryMethod` | Function | - | 查询数据方法（必需） |
| `addMethod` | Function | - | 新增数据方法 |
| `updateMethod` | Function | - | 更新数据方法 |
| `delMethod` | Function | - | 删除数据方法 |
| `columns` | Array | `[]` | 表格列配置（必需） |
| `name` | String | `''` | 数据名称，用于标题显示 |
| `showAddButton` | Boolean | `false` | 是否显示新增按钮 |
| `paging` | Boolean | `true` | 是否启用分页 |
| `defaultPageSize` | Number | `20` | 默认分页大小 |

### 7.2 表格配置Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dataTableSize` | String | `'medium'` | 表格尺寸：small/medium/large |
| `virtualScroll` | Boolean | `false` | 是否启用虚拟滚动 |
| `showSummary` | Boolean | `false` | 是否显示合计行 |
| `rowKey` | String | `'id'` | 行数据唯一标识字段 |
| `checkedRowKeys` | Array | `[]` | 默认选中的行 |
| `showSelectColumn` | Boolean | `true` | 是否显示选择列 |

### 7.3 表单配置Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `addOrEditShowMode` | String | `'modal'` | 新增/编辑显示模式：modal/drawer |
| `customFormStyle` | Object | `{}` | 自定义表单样式 |
| `labelWidth` | String | `'auto'` | 表单标签宽度 |
| `hideEmptyFormItems` | Boolean | `false` | 是否隐藏空表单项 |

### 7.4 导出配置Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showExport` | Boolean | `false` | 是否显示导出按钮 |
| `exportConfig` | Object | `{}` | 导出配置对象 |
| `showExportExcelTemplate` | Boolean | `true` | 是否显示导出模板按钮 |

### 7.5 Tab配置

```typescript
// Tab配置示例
const tabs = ref([
  {
    name: '1',
    tab: '全部项目',
    badge: 0,
    useBadge: true
  },
  {
    name: '2',
    tab: '进行中',
    badge: 5,
    useBadge: true
  },
  {
    name: '3',
    tab: '已完成',
    badge: 10,
    useBadge: true
  }
])
```

## 🔧 8. 列配置详解

### 8.1 基础列配置

```typescript
interface CRUDColumn {
  title: string          // 列标题
  key: string            // 数据键名
  width?: number         // 列宽度
  align?: string         // 对齐方式：left/center/right
  fixed?: string         // 固定列：left/right
  sorter?: boolean       // 是否可排序

  // 表单相关
  show?: boolean         // 是否在表单中显示
  required?: boolean     // 是否必填
  type?: string          // 控件类型
  placeholder?: string   // 占位符
  defaultValue?: any     // 默认值

  // 表格显示相关
  tableColumnShow?: boolean  // 是否在表格中显示
  tagRender?: boolean       // 是否使用标签渲染

  // 选择器相关
  selection?: Array<{label: string, value: any}>  // 选项列表
  dictType?: string         // 字典类型
}
```

### 8.2 常用控件类型

| 类型 | 说明 | 配置示例 |
|------|------|----------|
| `input` | 文本输入框 | `{type: 'input', placeholder: '请输入'}` |
| `select` | 下拉选择 | `{type: 'select', selection: options}` |
| `date` | 日期选择 | `{type: 'date'}` |
| `daterange` | 日期范围 | `{type: 'daterange'}` |
| `textarea` | 多行文本 | `{type: 'textarea', rows: 4}` |
| `number` | 数字输入 | `{type: 'number', min: 0}` |
| `switch` | 开关 | `{type: 'switch'}` |
| `upload` | 文件上传 | `{type: 'upload'}` |

### 8.3 列配置完整示例

```typescript
const columns = ref<CRUDColumn[]>([
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center',
    tableColumnShow: true,
    show: false  // 不在表单中显示
  },
  {
    title: '项目名称',
    key: 'projectName',
    width: 200,
    show: true,
    required: true,
    type: 'input',
    placeholder: '请输入项目名称'
  },
  {
    title: '项目状态',
    key: 'status',
    width: 120,
    align: 'center',
    show: true,
    type: 'select',
    selection: [
      {label: '进行中', value: '1'},
      {label: '已完成', value: '2'},
      {label: '已暂停', value: '3'}
    ],
    tagRender: true,  // 使用标签渲染
    defaultValue: '1'
  },
  {
    title: '开始时间',
    key: 'startTime',
    width: 180,
    align: 'center',
    show: true,
    type: 'date',
    required: true
  },
  {
    title: '项目描述',
    key: 'description',
    show: true,
    type: 'textarea',
    placeholder: '请输入项目描述',
    tableColumnShow: false  // 不在表格中显示
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
    show: false  // 操作列不在表单中显示
  }
])
```

## 🎯 9. 快速开始

### 9.1 选择合适的模板

**工作台页面模板** 适用于：
- 仪表板、首页
- 数据可视化页面
- 统计报表展示
- 无需查询表单的展示页面

**普通Container页面模板** 适用于：
- 需要查询条件但不需要完整CRUD功能的页面
- 数据计算和处理页面
- 报表查询和展示页面
- 数据导入导出页面

**CRUD页面模板** 适用于：
- 数据管理页面
- 需要增删改查的功能页面
- 带查询条件的列表页面
- 需要分页的表格页面

### 9.2 开发步骤

1. **复制对应模板代码**
2. **修改页面标题和路由**
3. **配置API方法**（CRUD页面）
4. **配置表格列**（CRUD页面）
5. **添加自定义查询条件**
6. **添加自定义操作按钮**
7. **调整样式和布局**

### 9.3 最佳实践

- ✅ 使用TypeScript类型定义
- ✅ 合理设置列宽度和对齐方式
- ✅ 为必填字段设置验证规则
- ✅ 使用语义化的变量命名
- ✅ 添加适当的注释说明
- ✅ 遵循项目的代码规范

### 9.4 模板选择决策树

```mermaid
graph TD
    A[开始选择页面模板] --> B{需要查询表单吗?}
    B -->|否| C[工作台页面模板]
    B -->|是| D{需要完整CRUD功能吗?}
    D -->|否| E[普通Container页面模板]
    D -->|是| F[CRUD页面模板]

    C --> G[适用场景:<br/>- 仪表板<br/>- 数据可视化<br/>- 统计报表]
    E --> H[适用场景:<br/>- 数据处理<br/>- 报表查询<br/>- 导入导出]
    F --> I[适用场景:<br/>- 数据管理<br/>- 表格操作<br/>- 增删改查]
```

三个模板都能自动处理高度计算，让你专注于业务逻辑实现！ 🚀
