/**
 * Electron-WebView 通信API类型定义
 * 提供完整的TypeScript类型支持，确保类型安全
 */

// ==================== 基础通信类型 ====================

/**
 * 消息类型枚举
 */
export enum ElectronMessageType {
  // 用户认证相关
  SET_TOKEN = 'SET_TOKEN',
  GET_TOKEN = 'GET_TOKEN',
  CLEAR_TOKEN = 'CLEAR_TOKEN',

  // 流程详情相关
  OPEN_PROCESS_DETAIL = 'OPEN_PROCESS_DETAIL',

  // 业务模块跳转相关
  NAVIGATE_TO_MODULE = 'NAVIGATE_TO_MODULE',
  NAVIGATE_TO_MENU = 'NAVIGATE_TO_MENU',

  // 系统状态相关
  GET_SYSTEM_LIST = 'GET_SYSTEM_LIST',
  GET_SYSTEM_WARN_NUM = 'GET_SYSTEM_WARN_NUM',
  SUBSCRIBE_SYSTEM_STATUS = 'SUBSCRIBE_SYSTEM_STATUS',
  UNSUBSCRIBE_SYSTEM_STATUS = 'UNSUBSCRIBE_SYSTEM_STATUS',

  // 响应消息
  RESPONSE = 'RESPONSE',
  ERROR = 'ERROR',

  // 事件通知
  SYSTEM_STATUS_CHANGED = 'SYSTEM_STATUS_CHANGED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
}

/**
 * 基础消息结构
 */
export interface BaseElectronMessage {
  /** 消息ID，用于请求响应匹配 */
  id: string
  /** 消息类型 */
  type: ElectronMessageType
  /** 时间戳 */
  timestamp: number
  /** 消息来源 */
  source: 'electron' | 'webview'
}

/**
 * 请求消息结构
 */
export interface ElectronRequestMessage<T = any> extends BaseElectronMessage {
  /** 请求数据 */
  data?: T
}

/**
 * 响应消息结构
 */
export interface ElectronResponseMessage<T = any> extends BaseElectronMessage {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 错误代码 */
  errorCode?: string
}

/**
 * 事件通知消息结构
 */
export interface ElectronEventMessage<T = any> extends BaseElectronMessage {
  /** 事件数据 */
  data: T
}

// ==================== 用户认证相关类型 ====================

/**
 * 用户TOKEN信息
 */
export interface UserTokenInfo {
  /** 访问令牌 */
  accessToken: string
  /** 刷新令牌 */
  refreshToken?: string
  /** 过期时间 */
  expiresAt?: number
  /** 用户信息 */
  userInfo?: {
    id: string
    username: string
    name: string
    avatar?: string
    [key: string]: any
  }
}

/**
 * 设置TOKEN请求参数
 */
export interface SetTokenRequest {
  tokenInfo: UserTokenInfo
}

/**
 * 获取TOKEN响应数据
 */
export interface GetTokenResponse {
  tokenInfo: UserTokenInfo | null
}

// ==================== 流程详情相关类型 ====================

/**
 * 打开流程详情请求参数
 */
export interface OpenProcessDetailRequest {
  /** 流程实例ID */
  processInstanceId: string
  /** 是否在新窗口打开 */
  openInNewWindow?: boolean
  /** 窗口配置 */
  windowOptions?: {
    width?: number
    height?: number
    modal?: boolean
  }
}

// ==================== 业务模块跳转相关类型 ====================

/**
 * 系统信息
 */
export interface SystemInfo {
  /** 系统ID */
  id: number
  /** 系统名称 */
  sysName: string
  /** 系统URL */
  sysUrl: string
  /** 系统图标 */
  icon?: string
  /** 系统颜色 */
  color?: string
  /** 警告数量 */
  warnNum?: number
  /** 是否启用 */
  status?: number
  /** 其他属性 */
  [key: string]: any
}

/**
 * 跳转到业务模块请求参数
 */
export interface NavigateToModuleRequest {
  /** 系统ID */
  systemId: number
  /** 是否仅加载路由（不跳转） */
  onlyLoad?: boolean
  /** 跳转完成后的回调路径 */
  callbackPath?: string
}

/**
 * 跳转到菜单请求参数
 */
export interface NavigateToMenuRequest {
  /** 系统ID */
  systemId: number
  /** 菜单路径 */
  menuPath: string
  /** 菜单参数 */
  menuParams?: Record<string, any>
  /** 是否在新窗口打开 */
  openInNewWindow?: boolean
}

// ==================== 系统状态相关类型 ====================

/**
 * 系统列表响应数据
 */
export interface GetSystemListResponse {
  /** 系统列表 */
  systems: SystemInfo[]
}

/**
 * 系统警告数量请求参数
 */
export interface GetSystemWarnNumRequest {
  /** 系统ID列表 */
  systemIds?: number[]
}

/**
 * 系统警告数量响应数据
 */
export interface GetSystemWarnNumResponse {
  /** 系统警告数量映射 */
  warnNums: Record<number, number>
}

/**
 * 系统状态变化事件数据
 */
export interface SystemStatusChangedEvent {
  /** 变化的系统ID */
  systemId: number
  /** 新的警告数量 */
  warnNum: number
  /** 变化类型 */
  changeType: 'warn_num_changed' | 'system_updated' | 'system_added' | 'system_removed'
  /** 完整的系统信息 */
  systemInfo?: SystemInfo
}

// ==================== API响应包装类型 ====================

/**
 * API调用结果
 */
export interface ElectronApiResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 响应数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 错误代码 */
  errorCode?: string
}

/**
 * 事件监听器类型
 */
export type ElectronEventListener<T = any> = (data: T) => void

/**
 * 事件取消订阅函数类型
 */
export type ElectronEventUnsubscribe = () => void

// ==================== 配置相关类型 ====================

/**
 * Electron通信配置
 */
export interface ElectronCommunicationConfig {
  /** 请求超时时间（毫秒） */
  timeout?: number
  /** 是否启用调试日志 */
  debug?: boolean
  /** 重试次数 */
  retryCount?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
}

// ==================== 错误相关类型 ====================

/**
 * Electron通信错误代码
 */
export enum ElectronErrorCode {
  /** 请求超时 */
  TIMEOUT = 'TIMEOUT',
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 参数错误 */
  INVALID_PARAMS = 'INVALID_PARAMS',
  /** 权限不足 */
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  /** 系统错误 */
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Electron通信错误
 */
export class ElectronCommunicationError extends Error {
  constructor(
    message: string,
    public code: ElectronErrorCode = ElectronErrorCode.UNKNOWN_ERROR,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'ElectronCommunicationError'
  }
}