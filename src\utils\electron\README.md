# Electron-WebView 通信API系统

## 🎯 概述

这是一套完整的Electron客户端与内嵌WebView中Vue应用的通信API系统，提供类型安全、错误处理和响应式支持。

## 📁 文件结构

```
src/utils/electron/
├── types.ts              # TypeScript类型定义
├── communication.ts      # 通信核心模块
├── api.ts               # API接口层
├── webview-handler.ts   # WebView端消息处理器
├── index.ts             # 统一入口和组合式API
└── README.md            # 本文档

electron/
├── main/
│   └── webview-bridge.js    # Electron主进程桥接器
└── preload/
    └── webview-preload.js   # 安全的preload脚本

docs/
└── electron-webview-api.md # 详细API文档

examples/
├── electron-api-demo.vue           # 完整演示组件
└── electron-integration-example.ts # 集成示例代码
```

## 🚀 快速开始

### 1. 基础使用

```typescript
import { ElectronBridge } from '@/utils/electron'

// 检查环境
if (ElectronBridge.utils.isElectron()) {
  console.log('运行在Electron环境中')
}

// 设置用户TOKEN
await ElectronBridge.auth.setToken({
  accessToken: 'your-token',
  userInfo: { id: '123', username: 'admin' }
})

// 打开流程详情
await ElectronBridge.process.openDetail('process-123')

// 跳转到业务模块
await ElectronBridge.navigation.toModule(2)
```

### 2. Vue组合式API

```vue
<script setup lang="ts">
import { useElectronAuth, useElectronSystem } from '@/utils/electron'

// 用户认证
const { tokenInfo, isAuthenticated, setToken } = useElectronAuth()

// 系统状态
const { systems, warnNums, subscribeStatus } = useElectronSystem()

// 订阅状态变化
subscribeStatus((event) => {
  console.log('系统状态变化:', event)
})
</script>
```

## 🔧 核心功能

### 用户认证管理
- ✅ TOKEN设置、获取、清除
- ✅ TOKEN过期监听
- ✅ 安全存储

### 流程详情管理
- ✅ 当前窗口打开流程详情
- ✅ 新窗口打开流程详情
- ✅ 与现有事件总线兼容

### 业务模块跳转
- ✅ 系统模块切换
- ✅ 动态路由加载
- ✅ 菜单路径跳转
- ✅ 新窗口打开支持

### 系统状态监控
- ✅ 响应式获取系统列表
- ✅ 实时警告数量更新
- ✅ 状态变化事件通知

## 🛡️ 安全特性

- **类型安全**: 完整的TypeScript支持
- **消息验证**: 验证消息来源和格式
- **上下文隔离**: 使用contextBridge和preload脚本
- **错误处理**: 统一的错误处理和重试机制

## 🔄 向下兼容

系统设计时充分考虑了向下兼容性：

```typescript
// 自动检测环境并回退
const handleProcessDetail = async (row: any) => {
  if (ElectronBridge.utils.isElectron()) {
    // 使用Electron API
    await ElectronBridge.process.openDetail(row.processInstanceId)
  } else {
    // 回退到原有事件总线
    useEventBus().emit('open-process-detail', {
      processInstanceId: row.processInstanceId,
    })
  }
}
```

## 📈 扩展性

### 添加新的API接口

1. **在types.ts中定义消息类型**
```typescript
export enum ElectronMessageType {
  // 添加新的消息类型
  NEW_FEATURE = 'NEW_FEATURE',
}

export interface NewFeatureRequest {
  // 定义请求参数
}
```

2. **在api.ts中添加API方法**
```typescript
export class ElectronAPI {
  static async newFeature(data: NewFeatureRequest) {
    return sendElectronRequest(ElectronMessageType.NEW_FEATURE, data)
  }
}
```

3. **在webview-handler.ts中添加处理逻辑**
```typescript
private async processMessage(message: ElectronRequestMessage): Promise<any> {
  switch (message.type) {
    case ElectronMessageType.NEW_FEATURE:
      return this.handleNewFeature(message.data)
    // ...
  }
}
```

4. **在webview-bridge.js中添加主进程处理**
```javascript
async processMessage(message) {
  switch (message.type) {
    case 'NEW_FEATURE':
      return this.handleNewFeature(message.data)
    // ...
  }
}
```

## 🧪 测试

### 运行演示
```bash
# 在Vue应用中访问演示页面
/examples/electron-api-demo
```

### 单元测试
```bash
npm run test:unit
```

### 集成测试
```bash
npm run test:e2e
```

## 📖 详细文档

查看 [完整API文档](../../../docs/electron-webview-api.md) 获取更多详细信息。

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

🎉 **现在您可以开始使用这套强大的Electron-WebView通信API系统了！**
