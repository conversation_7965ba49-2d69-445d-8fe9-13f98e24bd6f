# MermaidRenderer 组件使用指南

## 📋 组件概述

MermaidRenderer 是一个基于CDN的Mermaid图表渲染组件，采用单文件设计，避免过度工程化。支持实时编辑预览、多种视图模式、图表导出、主题切换等功能。

## ✨ 核心特性

- ✅ **CDN加载** - 动态加载Mermaid库，避免增加项目体积
- ✅ **实时预览** - 代码编辑实时渲染图表，支持防抖优化
- ✅ **多视图模式** - 支持代码、预览、分屏三种视图模式
- ✅ **图表导出** - 支持PNG和SVG格式导出
- ✅ **主题切换** - 内置多种主题，支持亮暗模式
- ✅ **全屏编辑** - 支持全屏模式，提升编辑体验
- ✅ **缩放拖拽** - 支持鼠标滚轮缩放、拖拽移动、双击重置
- ✅ **移动端适配** - 响应式设计，完美适配移动设备
- ✅ **错误处理** - 友好的错误提示和恢复机制

## 🚀 快速开始

### 基础用法

组件已全局注册为 `j-mermaid`，可直接在模板中使用：

```vue
<template>
  <j-mermaid 
    v-model="mermaidCode"
    height="400px"
    theme="default"
    default-view-mode="split"
  />
</template>

<script setup>
import { ref } from 'vue'

const mermaidCode = ref(`graph TD
    A[开始] --> B[处理]
    B --> C[结束]`)
</script>
```

### 高级用法

```vue
<template>
  <j-mermaid 
    v-model="mermaidCode"
    height="600px"
    theme="dark"
    default-view-mode="split"
    :show-toolbar="true"
    @render-success="handleRenderSuccess"
    @render-error="handleRenderError"
  />
</template>

<script setup>
import { ref } from 'vue'

const mermaidCode = ref(`sequenceDiagram
    participant A as 用户
    participant B as 系统
    A->>B: 登录请求
    B-->>A: 登录成功`)

const handleRenderSuccess = (svg) => {
  console.log('渲染成功:', svg)
}

const handleRenderError = (error) => {
  console.error('渲染失败:', error)
}
</script>
```

## 📖 API 文档

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | string | 示例流程图 | Mermaid 代码内容，支持 v-model 双向绑定 |
| theme | string | 'default' | 图表主题：default \| dark \| forest \| neutral |
| defaultViewMode | string | 'split' | 默认视图模式：code \| preview \| split |
| showToolbar | boolean | true | 是否显示工具栏 |
| height | string \| number | '600px' | 组件高度 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: string) | 代码内容变化时触发 |
| render-success | (svg: string) | 图表渲染成功时触发，返回SVG字符串 |
| render-error | (error: string) | 图表渲染失败时触发，返回错误信息 |

## 🎨 主题系统

组件内置4种主题：

- **default** - 默认主题，适合大多数场景
- **dark** - 暗色主题，适合暗色界面
- **forest** - 森林主题，绿色调
- **neutral** - 中性主题，灰色调

主题会自动与系统的亮暗模式联动。

## 📱 视图模式

### 桌面端
- **代码模式** - 只显示代码编辑器
- **预览模式** - 只显示图表预览
- **分屏模式** - 使用Naive UI Split组件，代码区域占35%，图表区域占65%

### 移动端
- **默认预览模式** - 移动端默认显示图表预览，优化小屏幕体验
- **分屏模式** - 自动切换为垂直布局，代码区域占40%，图表区域占60%
- 优化触摸操作体验
- 支持全屏编辑模式

### Split分屏特性
- **可调整大小** - 拖拽分割线调整代码和预览区域比例
- **智能布局** - 桌面端水平分屏，移动端垂直分屏
- **比例限制** - 代码区域占比限制在20%-60%之间（桌面端）或30%-70%（移动端）
- **响应式适配** - 根据屏幕尺寸自动调整布局方向

## 🔧 功能特性

### CDN加载管理
- 全局单例模式，防止重复加载
- 支持加载失败重试
- 显示加载进度状态

### 实时预览
- 代码变更实时渲染
- 防抖优化，避免频繁渲染
- 语法错误友好提示

### 图表导出
- **PNG格式导出** - 高分辨率位图，支持多重备选方案
  - 主要方案：Canvas + toBlob API
  - 备选方案：Canvas + toDataURL API
  - 自动降级：失败时切换为SVG格式
- **SVG格式导出** - 矢量图，保持最佳质量
- **智能错误处理** - 自动处理跨域和Canvas污染问题
- **自动文件命名** - 基于时间戳生成唯一文件名

### 缩放和拖拽
- **鼠标滚轮缩放** - 在图表上滚动鼠标滚轮进行缩放
- **拖拽移动** - 按住鼠标左键拖拽图表位置
- **双击重置** - 双击图表重置缩放和位置
- **工具栏控制** - 使用+/-按钮和重置按钮控制缩放
- **自适应显示** - 图表初始渲染时自动适应容器大小

### 全屏模式
- **完整工具栏** - 全屏模式包含与普通模式相同的完整工具栏
  - 视图切换按钮（代码/预览/分屏）
  - 主题切换选择器
  - 导出功能按钮（PNG/SVG）
  - 缩放控制按钮（放大/缩小/重置）
  - 退出全屏按钮
- **复用主组件** - 全屏模式复用主组件的所有功能，避免代码重复
- **同步视图模式** - 进入全屏时自动同步当前视图模式
- **独立控制** - 全屏内可独立切换视图模式和主题
- **ESC键退出** - 快速退出全屏模式
- **完整功能** - 支持所有缩放、拖拽、导出等功能

## 📚 支持的图表类型

- **流程图 (Flowchart)** - 展示流程和决策
- **时序图 (Sequence)** - 展示交互时序
- **甘特图 (Gantt)** - 项目进度管理
- **类图 (Class)** - 面向对象设计
- **状态图 (State)** - 状态转换
- **饼图 (Pie)** - 数据占比
- **用户旅程图 (Journey)** - 用户体验流程
- **Git图 (Git)** - 版本控制流程
- **实体关系图 (ER)** - 数据库设计
- **思维导图 (Mindmap)** - 思维整理

## 🛠️ 开发指南

### 本地开发

1. 组件位于 `src/components/MermaidRenderer.vue`
2. 已在 `src/utils/global.ts` 中全局注册
3. TypeScript类型声明在 `src/types/jp.d.ts`

### 自定义扩展

如需自定义功能，可以：

1. 修改主题配置
2. 添加新的导出格式
3. 扩展工具栏功能
4. 自定义错误处理

### 性能优化

- CDN加载采用单例模式
- 图表渲染使用防抖处理
- 支持大型图表的懒加载
- 内存泄漏防护机制

## 🔍 故障排除

### 常见问题

**Q: 图表不显示？**
A: 检查网络连接，确保CDN可以正常加载

**Q: 代码编辑没有语法高亮？**
A: 组件使用JavaScript语法高亮模拟Mermaid语法

**Q: 导出功能不工作？**
A: 确保图表已正确渲染，检查浏览器是否支持Canvas API

**Q: 移动端体验不佳？**
A: 组件已优化移动端体验，如有问题请检查CSS样式

### 调试技巧

1. 打开浏览器开发者工具查看控制台错误
2. 检查网络面板确认CDN加载状态
3. 使用组件的错误事件监听问题
4. 查看Mermaid官方文档确认语法正确性

## 📝 更新日志

### v1.0.0 (2025-01-21)
- ✅ 初始版本发布
- ✅ 支持基础图表渲染
- ✅ 实现多视图模式
- ✅ 添加导出功能
- ✅ 完成移动端适配
- ✅ 创建完整教程系统

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进组件：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本组件遵循项目的开源许可证。

---

## 🔗 相关链接

- [Mermaid 官方文档](https://mermaid.js.org/)
- [Naive UI 组件库](https://www.naiveui.com/)
- [Demo 演示系统](/demo/mermaid)

---

*最后更新：2025-01-21*
