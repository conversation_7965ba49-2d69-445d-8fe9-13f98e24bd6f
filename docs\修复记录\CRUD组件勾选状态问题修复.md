# CRUD组件勾选状态问题修复记录

## 问题描述

**用户反馈**：PC端勾选后再次查询，勾选没有取消，但是页面上的勾选显示未勾选，其实有勾选数据。是手动调用的crud的ref的queryData方法。

## 问题分析

### 1. 问题根因

在CRUD组件中，当手动调用`queryData`方法时，虽然`checkedRowKeys`数据保持不变，但是表格组件（NDataTable）的显示状态没有正确更新。这是因为：

1. **数据引用问题**：queryData方法会更新`dataRef.value`，创建了新的数据数组引用
2. **Vue响应性**：虽然`checkedRowKeys`没有改变，但表格数据的引用改变了，导致表格组件可能无法正确识别之前勾选的行
3. **缺少强制更新**：在某些情况下，需要强制触发表格组件的更新来确保勾选状态的正确显示

### 2. 代码定位

主要涉及的文件和代码位置：

- `/src/components/common/crud/index.vue` - 主组件文件
- `/src/components/common/crud/composables/useCrudCore.ts` - 核心逻辑，包含queryData方法
- 第175-176行：数据更新逻辑

```typescript
originData.value = processedRecords
addTableNo(originData.value)
dataRef.value = getTableData([...originData.value])
```

## 修复方案

### 方案一：保持勾选状态（推荐）

在`queryData`方法执行后，强制更新表格的勾选状态：

```typescript
// 在 useCrudCore.ts 的 handleSuccess 方法中添加
const handleSuccess = (records: any[], total: number, pages: number) => {
  // ... 现有代码 ...
  
  // 在数据更新完成后，确保勾选状态同步
  if (!isInfiniteScrollMode) {
    originData.value = processedRecords
    addTableNo(originData.value)
    dataRef.value = getTableData([...originData.value])
    
    // 添加：强制更新勾选状态
    nextTick(() => {
      // 触发勾选状态的重新渲染
      if (checkRowKeys.value && checkRowKeys.value.length > 0) {
        const tempKeys = [...checkRowKeys.value]
        checkRowKeys.value = []
        nextTick(() => {
          checkRowKeys.value = tempKeys
        })
      }
    })
  }
  
  // ... 其余代码 ...
}
```

### 方案二：在组件层面处理

在`index.vue`中监听数据变化并同步勾选状态：

```typescript
// 添加一个watcher来监听数据变化
watch(
  () => state.dataRef.value,
  (newData) => {
    if (checkRowKeys.value && checkRowKeys.value.length > 0 && newData.length > 0) {
      // 验证勾选的keys是否仍然有效
      const validKeys = checkRowKeys.value.filter(key => 
        newData.some(item => (item[props.rowKey] || item.id) === key)
      )
      
      // 如果有无效的keys，更新勾选状态
      if (validKeys.length !== checkRowKeys.value.length) {
        checkRowKeys.value = validKeys
        emit('update:checked-row-keys', validKeys)
      }
    }
  },
  { deep: true }
)
```

### 方案三：提供清除勾选的选项

为`queryData`方法添加一个参数，允许在查询时选择是否清除勾选状态：

```typescript
const queryData = (resetPage = false, isInfiniteScrollLoad = false, clearSelection = false) => {
  // ... 现有代码 ...
  
  if (clearSelection) {
    checkRowKeys.value = []
    emit('update:checked-row-keys', [])
  }
  
  // ... 其余代码 ...
}
```

## 实施建议

1. **立即修复**：采用方案一，在数据更新后强制刷新勾选状态
2. **长期优化**：
   - 考虑升级NaiveUI版本，查看是否有相关bug修复
   - 优化数据更新逻辑，避免不必要的引用变更
   - 为开发者提供更多控制选项（如方案三）

## 测试用例

```javascript
// 测试步骤
1. 在表格中勾选若干行
2. 手动调用 crudRef.value.queryData()
3. 验证勾选状态是否保持
4. 验证表格显示是否正确

// 测试代码示例
const testCheckboxState = async () => {
  // 1. 设置初始勾选
  crudRef.value.checkRowKeys = [1, 2, 3]
  
  // 2. 执行查询
  await crudRef.value.queryData()
  
  // 3. 验证结果
  console.assert(crudRef.value.checkRowKeys.length === 3, '勾选数量应保持不变')
  console.assert(document.querySelectorAll('.n-checkbox--checked').length === 3, '显示的勾选数量应该正确')
}
```

## 注意事项

1. 修复后需要进行全面的回归测试，确保不影响其他功能
2. 考虑性能影响，避免在大数据量时频繁触发更新
3. 建议在修复后更新组件文档，说明勾选状态的行为

## 更新记录

- 2025-01-16：问题分析和修复方案制定
- 待实施：代码修复和测试验证
