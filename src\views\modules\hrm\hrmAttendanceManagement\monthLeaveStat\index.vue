<template>
  <j-crud
    :queryMethod="queryMonthLeaveStat"
    :columns="columns"
    :singleLine="false"
    :queryForm="queryForm"
    name="月考勤统计"
    :paging="false"
    :show-add-button="false"
    :show-operation-button="false"
    :virtualScroll="true"
    :show-export="true"
    :show-exportExcel-template="false"
    :export-config="exportConfig"
    ref="crudRef"
  >
    <!-- 拓展表单项 -->
    <template #extendFormItems>
      <n-form-item label="统计年份">
        <n-date-picker
          v-model:formatted-value="queryForm.yearMonth"
          type="month"
          value-format="yyyy-MM"
          @update-formatted-value="updateFormattedValue"
        />
      </n-form-item>
      <n-form-item label="科室">
        <j-bus-hos-org v-model:value="queryForm.orgIds" :multiple="true" />
      </n-form-item>
      <n-form-item label="职工">
        <j-bus-emp-search v-model:value="queryForm.empCodes" :multiple="true" />
      </n-form-item>
      <n-form-item label="职工类型">
        <n-tree-select
          v-model:value="queryForm.empType"
          :options="hrmDictStore.getSelectOptions('EMP_TYPE')"
          key-field="id"
          label-field="codeLable"
          check-strategy="child"
          :multiple="true"
          :default-expand-all="true"
          max-tag-count="responsive"
          :consistent-menu-width="false"
          clearable
        >
        </n-tree-select>
      </n-form-item>
      <n-form-item label="执业资格">
        <n-tree-select
          v-model:value="queryForm.practicingType"
          :options="hrmDictStore.getSelectOptions('PRACTICING_ABILITY')"
          key-field="id"
          label-field="codeLable"
          check-strategy="child"
          :multiple="true"
          :default-expand-all="true"
          max-tag-count="responsive"
          :consistent-menu-width="false"
          clearable
        >
        </n-tree-select>
      </n-form-item>
      <n-form-item label="职称">
        <n-input v-model:value="queryForm.engageRank" clearable></n-input>
      </n-form-item>
    </template>
  </j-crud>
</template>

<script lang="ts" setup>
  import { NPopover, NButton } from 'naive-ui'
  import { onMounted, ref, h, nextTick } from 'vue'
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import { queryMonthLeaveStat, updateMonthLeaveStatRemark } from '@/api/hrm/attendanceManagement/hrmLeave'
  import { useSysStore } from '@/store'
  import { ExcelType } from '@/utils/excel'
  import JPGlobal from '@jutil'
  import { useRouter } from 'vue-router'
  import dayjs from 'dayjs'
  import { useHrmDictStore } from '@/store/hrm/hrmDictStore'

  //const数据
  const sysStore = useSysStore()
  const hrmDictStore = useHrmDictStore()
  const crudRef = ref()
  const router = useRouter()
  //data数据

  //导出数据配置
  let exportConfig = ref<ExcelType>({})


  //查询条件表单
  let queryForm = ref({
    yearMonth: JPGlobal.timestampToTime(new Date().setMonth(new Date().getMonth() - 1), 'yyyy-MM'),
    orgIds: [],
    practicingType: [],
    empType: [],
    empCodes: [],
    engageRank: '',
    isCheck: '',
  })

  //表格columns
  let columns = ref<CRUDColumnInterface[]>([
    {
      title: '序号',
      key: 'index',
      align: 'center',
      fixed: 'left',
      width: 50,
      resizable: true,
    },
    {
      title: '科室',
      key: 'orgName',
      align: 'center',
      fixed: 'left',
      width: 100,
      resizable: true,
    },
    {
      title: '总人数',
      key: 'total',
      align: 'center',
      fixed: 'left',
      width: 60,
      resizable: true,
    },
    {
      title: '医生',
      key: 'doctor',
      align: 'center',
      fixed: 'left',
      width: 50,
      resizable: true,
    },
    {
      title: '护士',
      key: 'nurse',
      align: 'center',
      fixed: 'left',
      width: 50,
      resizable: true,
    },
    {
      title: '技师',
      key: 'technician',
      align: 'center',
      fixed: 'left',
      width: 50,
      resizable: true,
    },
    {
      title: '排班未确认数量',
      key: 'noConfirm',
      realKey: 'noConfirm',
      align: 'center',
      width: 50,
      fixed: 'left',
      resizable: true,
      render: row => {
        return h(
          NPopover,
          {
            trigger: 'hover',
            placement: 'top',
          },
          {
            trigger: () =>
              h(
                'span',
                {
                  style: {
                    color: row.noConfirm > 0 ? 'red' : '',
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    router.push({
                      path: '/hrm/hrmWorkforceManagement/monthScduQuery',
                      query: {
                        orgId: row.orgId,
                        yearMonth: dayjs(queryForm.value.yearMonth).valueOf(),
                      },
                    })
                  },
                },
                row.noConfirm
              ),
            default: () => h('div', {}, row.noConfirmEmp?.map((emp: any) => emp.empName).join('、') || '无'),
          }
        )
      },
    },
    {
      title: '具体人员具体请假天数',
      key: 'detail3',
      align: 'center',
      fixed: 'left',
      resizable: true,
    },
    {
      title: '备注',
      key: 'remark',
      align: 'center',
      fixed: 'left',
      resizable: true,
      render: row => {
        // 如果isCheck等于"1"，显示"已复核"，否则显示原始备注
        if (row.isCheck === '1') {
          return '已复核'
        }
        return row.remark || ''
      },
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable: true,
      width: 100,
      render: row => {
        return h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            onClick: () => confirmReview(row),
          },
          { default: () => '复核确认' }
        )
      },
    },
  ])

  //methods方法
  //确认复核
  const confirmReview = (row: any) => {
    window.$dialog?.warning({
      title: '确认复核',
      content: '确定要对该记录进行复核确认吗？',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: async () => {
        if (!row.remark) {
          // 如果备注为空，则设置为已复核
          row.remark = '已复核'
        } else if (!row.remark.includes('已复核')) {
          row.remark = row.remark + ' 已复核'
        }
        // 更新复核状态
        try {
          await updateMonthLeaveStatRemark({
            orgId: row.orgId,
            queryDate: queryForm.value.yearMonth,
          })
          window.$message?.success('复核确认成功')
        } catch (error) {
          window.$message?.error('保存失败，请重试')
        }
      },
    })
  }

  //年度改变
  const updateFormattedValue = (value: string, timestampValue: number | null) => {
    queryForm.value.yearMonth = value
    exportConfig.value = {
      tableRef: crudRef.value.dataTable,
      excelName: queryForm.value.yearMonth + '月考勤统计',
    }
    crudRef.value.queryData()
  }

  //初始化onMounted
  onMounted(async () => {
    nextTick(() => {
      //导出数据配置
      exportConfig.value = {
        tableRef: crudRef.value?.dataTable,
        excelName: queryForm.value?.yearMonth + '月考勤统计',
      }
      crudRef.value?.setExport()
    })
  })
</script>

<script lang="ts">
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: '月考勤统计2',
  })
</script>
