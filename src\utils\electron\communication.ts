/**
 * Electron-WebView 通信核心模块
 * 提供统一的通信接口和消息处理机制
 */

import {
  ElectronMessageType,
  BaseElectronMessage,
  ElectronRequestMessage,
  ElectronResponseMessage,
  ElectronEventMessage,
  ElectronApiResult,
  ElectronEventListener,
  ElectronEventUnsubscribe,
  ElectronCommunicationConfig,
  ElectronCommunicationError,
  ElectronErrorCode,
} from './types'

/**
 * 默认配置
 */
const DEFAULT_CONFIG: Required<ElectronCommunicationConfig> = {
  timeout: 10000, // 10秒超时
  debug: false,
  retryCount: 3,
  retryInterval: 1000,
}

/**
 * Electron通信管理器
 */
export class ElectronCommunicationManager {
  private config: Required<ElectronCommunicationConfig>
  private pendingRequests = new Map<string, {
    resolve: (value: any) => void
    reject: (error: Error) => void
    timer: NodeJS.Timeout
  }>()
  private eventListeners = new Map<ElectronMessageType, Set<ElectronEventListener>>()
  private isElectronEnvironment = false

  constructor(config: ElectronCommunicationConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    this.isElectronEnvironment = this.detectElectronEnvironment()
    this.initializeMessageListener()
  }

  /**
   * 检测是否在Electron环境中
   */
  private detectElectronEnvironment(): boolean {
    try {
      // 检测是否在Electron的webview中
      return !!(window as any).electronAPI || 
             !!(window as any).require || 
             navigator.userAgent.toLowerCase().includes('electron')
    } catch {
      return false
    }
  }

  /**
   * 初始化消息监听器
   */
  private initializeMessageListener(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleMessage.bind(this))
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = event.data as BaseElectronMessage

      if (!message || !message.type || !message.id) {
        return
      }

      this.debugLog('收到消息:', message)

      // 处理响应消息
      if (message.type === ElectronMessageType.RESPONSE || message.type === ElectronMessageType.ERROR) {
        this.handleResponseMessage(message as ElectronResponseMessage)
        return
      }

      // 处理事件通知消息
      this.handleEventMessage(message as ElectronEventMessage)
    } catch (error) {
      this.debugLog('处理消息时发生错误:', error)
    }
  }

  /**
   * 处理响应消息
   */
  private handleResponseMessage(message: ElectronResponseMessage): void {
    const pending = this.pendingRequests.get(message.id)
    if (!pending) {
      return
    }

    clearTimeout(pending.timer)
    this.pendingRequests.delete(message.id)

    if (message.success) {
      pending.resolve(message.data)
    } else {
      const error = new ElectronCommunicationError(
        message.error || '未知错误',
        message.errorCode as ElectronErrorCode || ElectronErrorCode.UNKNOWN_ERROR
      )
      pending.reject(error)
    }
  }

  /**
   * 处理事件通知消息
   */
  private handleEventMessage(message: ElectronEventMessage): void {
    const listeners = this.eventListeners.get(message.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(message.data)
        } catch (error) {
          this.debugLog('事件监听器执行错误:', error)
        }
      })
    }
  }

  /**
   * 发送请求消息
   */
  private async sendRequest<T = any>(
    type: ElectronMessageType,
    data?: any,
    retryCount = this.config.retryCount
  ): Promise<T> {
    if (!this.isElectronEnvironment) {
      throw new ElectronCommunicationError(
        '当前不在Electron环境中',
        ElectronErrorCode.SYSTEM_ERROR
      )
    }

    const messageId = this.generateMessageId()
    const message: ElectronRequestMessage = {
      id: messageId,
      type,
      timestamp: Date.now(),
      source: 'webview',
      data,
    }

    return new Promise((resolve, reject) => {
      // 设置超时定时器
      const timer = setTimeout(() => {
        this.pendingRequests.delete(messageId)
        if (retryCount > 0) {
          // 重试
          this.debugLog(`请求超时，正在重试... 剩余重试次数: ${retryCount - 1}`)
          setTimeout(() => {
            this.sendRequest<T>(type, data, retryCount - 1)
              .then(resolve)
              .catch(reject)
          }, this.config.retryInterval)
        } else {
          reject(new ElectronCommunicationError('请求超时', ElectronErrorCode.TIMEOUT))
        }
      }, this.config.timeout)

      // 保存待处理请求
      this.pendingRequests.set(messageId, { resolve, reject, timer })

      // 发送消息
      try {
        this.postMessage(message)
        this.debugLog('发送请求:', message)
      } catch (error) {
        clearTimeout(timer)
        this.pendingRequests.delete(messageId)
        reject(new ElectronCommunicationError(
          '发送消息失败',
          ElectronErrorCode.NETWORK_ERROR,
          error as Error
        ))
      }
    })
  }

  /**
   * 发送消息到Electron主进程
   */
  private postMessage(message: BaseElectronMessage): void {
    if ((window as any).electronAPI?.postMessage) {
      // 使用Electron的preload脚本提供的API
      (window as any).electronAPI.postMessage(message)
    } else if (window.parent !== window) {
      // 使用postMessage API（适用于webview）
      window.parent.postMessage(message, '*')
    } else {
      throw new Error('无法找到有效的消息发送方式')
    }
  }

  /**
   * 生成唯一消息ID
   */
  private generateMessageId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 调试日志
   */
  private debugLog(...args: any[]): void {
    if (this.config.debug) {
      console.log('[ElectronCommunication]', ...args)
    }
  }

  /**
   * 发送API请求
   */
  public async sendApiRequest<T = any>(
    type: ElectronMessageType,
    data?: any
  ): Promise<ElectronApiResult<T>> {
    try {
      const result = await this.sendRequest<T>(type, data)
      return {
        success: true,
        data: result,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        errorCode: error instanceof ElectronCommunicationError ? error.code : ElectronErrorCode.UNKNOWN_ERROR,
      }
    }
  }

  /**
   * 监听事件
   */
  public addEventListener<T = any>(
    type: ElectronMessageType,
    listener: ElectronEventListener<T>
  ): ElectronEventUnsubscribe {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set())
    }
    
    const listeners = this.eventListeners.get(type)!
    listeners.add(listener)

    // 返回取消订阅函数
    return () => {
      listeners.delete(listener)
      if (listeners.size === 0) {
        this.eventListeners.delete(type)
      }
    }
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: ElectronMessageType, listener?: ElectronEventListener): void {
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      if (listener) {
        listeners.delete(listener)
      } else {
        listeners.clear()
      }
      
      if (listeners.size === 0) {
        this.eventListeners.delete(type)
      }
    }
  }

  /**
   * 清理所有资源
   */
  public dispose(): void {
    // 清理所有待处理请求
    this.pendingRequests.forEach(({ timer, reject }) => {
      clearTimeout(timer)
      reject(new ElectronCommunicationError('通信管理器已销毁', ElectronErrorCode.SYSTEM_ERROR))
    })
    this.pendingRequests.clear()

    // 清理所有事件监听器
    this.eventListeners.clear()

    // 移除消息监听器
    if (typeof window !== 'undefined') {
      window.removeEventListener('message', this.handleMessage.bind(this))
    }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): Required<ElectronCommunicationConfig> {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ElectronCommunicationConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 检查是否在Electron环境中
   */
  public isElectron(): boolean {
    return this.isElectronEnvironment
  }
}

// 创建全局实例
export const electronCommunication = new ElectronCommunicationManager({
  debug: import.meta.env.DEV, // 开发环境启用调试
})

// 导出便捷方法
export const sendElectronRequest = electronCommunication.sendApiRequest.bind(electronCommunication)
export const addElectronEventListener = electronCommunication.addEventListener.bind(electronCommunication)
export const removeElectronEventListener = electronCommunication.removeEventListener.bind(electronCommunication)
