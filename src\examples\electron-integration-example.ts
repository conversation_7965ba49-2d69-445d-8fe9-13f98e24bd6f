/**
 * Electron集成示例
 * 展示如何在现有Vue组件中集成Electron API
 */

import { ElectronBridge, useElectronAuth, useElectronSystem } from '@/utils/electron'
import { useEventBus } from '@/utils/eventBus'
import { useSysStore } from '@/store'
import { addDynamicRoute } from '@/router'

// ==================== 示例1: 改造现有的流程详情触发 ====================

/**
 * 原有的流程详情处理方法
 */
export const originalHandleProcessDetail = (row: any) => {
  const eventBus = useEventBus()
  eventBus.emit('open-process-detail', {
    processInstanceId: row.processInstanceId,
  })
}

/**
 * 集成Electron后的流程详情处理方法
 */
export const enhancedHandleProcessDetail = async (row: any) => {
  // 检查是否在Electron环境中
  if (ElectronBridge.utils.isElectron()) {
    try {
      // 使用Electron API打开流程详情
      const result = await ElectronBridge.process.openDetail(row.processInstanceId)
      
      if (result.success) {
        console.log('✅ Electron环境：流程详情已打开')
        return
      } else {
        console.warn('⚠️ Electron API调用失败，回退到原有逻辑:', result.error)
      }
    } catch (error) {
      console.error('❌ Electron API异常，回退到原有逻辑:', error)
    }
  }

  // 回退到原有的事件总线机制（保持向下兼容）
  originalHandleProcessDetail(row)
}

// ==================== 示例2: 改造业务模块跳转 ====================

/**
 * 原有的toHome方法（简化版）
 */
export const originalToHome = async (item: any, onlyLoad = false) => {
  const sysStore = useSysStore()
  
  // 设置系统信息
  sysStore.setSystemInfo({
    systemId: item.id,
    systemName: item.sysName,
    sysUrl: item.sysUrl,
  })

  // 加载动态路由
  await addDynamicRoute(item.id)

  if (!onlyLoad) {
    // 跳转到工作台
    const router = (await import('vue-router')).useRouter()
    router.push({ path: '/home' })
  }
}

/**
 * 集成Electron后的toHome方法
 */
export const enhancedToHome = async (item: any, onlyLoad = false) => {
  // 检查是否在Electron环境中
  if (ElectronBridge.utils.isElectron()) {
    try {
      // 使用Electron API进行模块跳转
      const result = await ElectronBridge.navigation.toModule(item.id, {
        onlyLoad,
        callbackPath: '/home'
      })
      
      if (result.success) {
        console.log('✅ Electron环境：模块跳转成功')
        return
      } else {
        console.warn('⚠️ Electron API调用失败，回退到原有逻辑:', result.error)
      }
    } catch (error) {
      console.error('❌ Electron API异常，回退到原有逻辑:', error)
    }
  }

  // 回退到原有逻辑（保持向下兼容）
  await originalToHome(item, onlyLoad)
}

// ==================== 示例3: 系统状态监控集成 ====================

/**
 * 集成Electron系统状态监控的组合式函数
 */
export const useEnhancedSystemStatus = () => {
  const { systems, warnNums, subscribeStatus } = useElectronSystem()

  // 如果在Electron环境中，启用响应式状态监控
  if (ElectronBridge.utils.isElectron()) {
    // 订阅系统状态变化
    subscribeStatus((event) => {
      console.log('📊 系统状态变化:', event)
      
      // 这里可以触发UI更新或其他业务逻辑
      if (event.changeType === 'warn_num_changed') {
        window.$message?.info(`系统 ${event.systemId} 警告数量更新为 ${event.warnNum}`)
      }
    })

    // 定期获取系统状态
    const refreshSystemStatus = async () => {
      try {
        await ElectronBridge.system.getList()
        await ElectronBridge.system.getWarnNum()
      } catch (error) {
        console.error('获取系统状态失败:', error)
      }
    }

    // 每30秒刷新一次
    const intervalId = setInterval(refreshSystemStatus, 30000)

    // 返回清理函数
    const cleanup = () => {
      clearInterval(intervalId)
      ElectronBridge.system.unsubscribe()
    }

    return {
      systems,
      warnNums,
      refreshSystemStatus,
      cleanup,
    }
  }

  // 非Electron环境返回空实现
  return {
    systems: ref([]),
    warnNums: ref({}),
    refreshSystemStatus: () => Promise.resolve(),
    cleanup: () => {},
  }
}

// ==================== 示例4: 用户认证集成 ====================

/**
 * 集成Electron用户认证的组合式函数
 */
export const useEnhancedAuth = () => {
  const { tokenInfo, isAuthenticated, setToken, getToken, clearToken } = useElectronAuth()

  // 登录方法
  const login = async (credentials: { username: string; password: string }) => {
    try {
      // 这里应该调用实际的登录API
      const loginResponse = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
      })

      const loginData = await loginResponse.json()

      if (loginData.success) {
        // 如果在Electron环境中，使用Electron API存储TOKEN
        if (ElectronBridge.utils.isElectron()) {
          await setToken({
            accessToken: loginData.accessToken,
            refreshToken: loginData.refreshToken,
            expiresAt: Date.now() + loginData.expiresIn * 1000,
            userInfo: loginData.userInfo,
          })
        } else {
          // 非Electron环境使用localStorage
          localStorage.setItem('access_token', loginData.accessToken)
          localStorage.setItem('user_info', JSON.stringify(loginData.userInfo))
        }

        return { success: true, data: loginData }
      } else {
        return { success: false, error: loginData.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: '登录请求失败' }
    }
  }

  // 登出方法
  const logout = async () => {
    try {
      // 调用登出API
      await fetch('/api/auth/logout', { method: 'POST' })

      // 清除TOKEN
      if (ElectronBridge.utils.isElectron()) {
        await clearToken()
      } else {
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_info')
      }

      return { success: true }
    } catch (error) {
      console.error('登出失败:', error)
      return { success: false, error: '登出请求失败' }
    }
  }

  // 初始化时获取TOKEN
  const initializeAuth = async () => {
    if (ElectronBridge.utils.isElectron()) {
      await getToken()
    } else {
      // 从localStorage恢复TOKEN信息
      const accessToken = localStorage.getItem('access_token')
      const userInfoStr = localStorage.getItem('user_info')
      
      if (accessToken && userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr)
          // 这里可以设置到响应式状态中
          console.log('从localStorage恢复用户信息:', userInfo)
        } catch (error) {
          console.error('解析用户信息失败:', error)
        }
      }
    }
  }

  return {
    tokenInfo,
    isAuthenticated,
    login,
    logout,
    initializeAuth,
  }
}

// ==================== 示例5: 菜单跳转集成 ====================

/**
 * 通用的菜单跳转方法
 */
export const navigateToMenu = async (
  systemId: number,
  menuPath: string,
  options: {
    menuParams?: Record<string, any>
    openInNewWindow?: boolean
    fallbackToRouter?: boolean
  } = {}
) => {
  const { menuParams, openInNewWindow = false, fallbackToRouter = true } = options

  // 检查是否在Electron环境中
  if (ElectronBridge.utils.isElectron()) {
    try {
      const result = await ElectronBridge.navigation.toMenu(systemId, menuPath, {
        menuParams,
        openInNewWindow,
      })
      
      if (result.success) {
        console.log('✅ Electron环境：菜单跳转成功')
        return { success: true }
      } else {
        console.warn('⚠️ Electron菜单跳转失败:', result.error)
      }
    } catch (error) {
      console.error('❌ Electron菜单跳转异常:', error)
    }
  }

  // 回退到Vue Router（如果允许）
  if (fallbackToRouter) {
    try {
      const router = (await import('vue-router')).useRouter()
      await router.push({
        path: menuPath,
        query: menuParams,
      })
      console.log('✅ 使用Vue Router跳转成功')
      return { success: true }
    } catch (error) {
      console.error('❌ Vue Router跳转失败:', error)
      return { success: false, error: 'Router跳转失败' }
    }
  }

  return { success: false, error: '所有跳转方式都失败' }
}

// ==================== 示例6: 错误处理和重试机制 ====================

/**
 * 带重试机制的API调用包装器
 */
export const withRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries = 3,
  retryDelay = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error as Error
      console.warn(`API调用失败 (尝试 ${attempt}/${maxRetries}):`, error)

      if (attempt < maxRetries) {
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
      }
    }
  }

  throw lastError!
}

/**
 * 使用重试机制的示例
 */
export const robustProcessDetailOpen = async (processInstanceId: string) => {
  return withRetry(
    () => ElectronBridge.process.openDetail(processInstanceId),
    3,
    1000
  )
}

// ==================== 示例7: 性能监控 ====================

/**
 * API调用性能监控
 */
export const withPerformanceMonitoring = async <T>(
  apiName: string,
  apiCall: () => Promise<T>
): Promise<T> => {
  const startTime = performance.now()
  
  try {
    const result = await apiCall()
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.log(`📊 API性能监控 - ${apiName}: ${duration.toFixed(2)}ms`)
    
    // 如果调用时间过长，发出警告
    if (duration > 5000) {
      console.warn(`⚠️ API调用耗时过长: ${apiName} (${duration.toFixed(2)}ms)`)
    }
    
    return result
  } catch (error) {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.error(`❌ API调用失败 - ${apiName}: ${duration.toFixed(2)}ms`, error)
    throw error
  }
}

// 使用示例
export const monitoredSystemListGet = () => {
  return withPerformanceMonitoring(
    'getSystemList',
    () => ElectronBridge.system.getList()
  )
}
