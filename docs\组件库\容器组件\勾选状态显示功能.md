# Container组件勾选状态显示功能

**开发人员**: Dwsy  
**开发日期**: 2025-01-22  
**功能编号**: CONTAINER-CHECKED-001

## 功能概述

在Container组件的分页区域显示已勾选项目的数量，格式为"已选择X条"，与CRUD组件完美集成。

## 功能特性

### ✨ 核心功能
- **实时显示**: 勾选状态变化时，分页区域的数量实时更新
- **跨页统计**: 支持跨页勾选状态的统计显示
- **无缝集成**: 与现有CRUD组件完美集成，无需额外配置
- **向下兼容**: 不影响现有功能，保持向下兼容性

### 🎯 显示位置
- 桌面端：分页组件的prefix插槽中显示
- 移动端：暂不显示（可后续扩展）

## 技术实现

### 1. Container组件修改

#### Props新增
```typescript
interface ContainerProps {
  // ... 其他props
  checkedRowKeys?: (string | number)[] // 新增：已勾选的行键
}
```

#### 计算属性
```typescript
// 计算已勾选的数量
const checkedRowKeysCount = computed(() => {
  return props.checkedRowKeys ? props.checkedRowKeys.length : 0
})
```

#### 模板修改
```vue
<template #prefix="{}"> 已选择{{ checkedRowKeysCount }}条 </template>
```

### 2. CRUD组件修改

在j-container标签中添加checkedRowKeys属性传递：

```vue
<j-container
  :checkedRowKeys="checkRowKeys"
  // ... 其他props
>
```

## 使用方法

### 基础使用
使用CRUD组件时，勾选状态会自动显示在分页区域：

```vue
<template>
  <j-crud
    :queryMethod="queryMethod"
    :columns="columns"
    v-model:checked-row-keys="checkRowKeys"
    :paging="true"
  />
</template>
```

### 直接使用Container组件
如果直接使用Container组件，需要手动传递checkedRowKeys：

```vue
<template>
  <j-container
    :checkedRowKeys="selectedKeys"
    :pagination="{ show: true, total: 100 }"
  >
    <template #content>
      <!-- 你的内容 -->
    </template>
  </j-container>
</template>

<script setup>
const selectedKeys = ref([1, 2, 3]) // 已勾选的键值
</script>
```

## 演示页面

演示页面位置：`src/views/modules/demo/components/container/checked-rows-demo.vue`

### 演示功能
- 勾选项目查看数量变化
- 跨页勾选状态统计
- 批量操作功能演示
- 选中项详情查看

## 注意事项

### ⚠️ 重要提醒
1. **数据类型**: checkedRowKeys必须是数组类型，支持string或number类型的键值
2. **性能考虑**: 大量数据时，计算属性会自动优化，无需担心性能问题
3. **移动端**: 当前版本仅在桌面端显示，移动端可后续扩展
4. **兼容性**: 完全向下兼容，不传递checkedRowKeys时显示"已选择0条"

### 🔧 扩展建议
1. 可以考虑添加选中项的详细信息显示
2. 可以在移动端分页组件中也添加类似功能
3. 可以添加快速清空选择的功能按钮

## 测试验证

### 测试用例
1. ✅ 勾选项目时数量正确显示
2. ✅ 取消勾选时数量正确更新
3. ✅ 翻页后勾选数量保持正确
4. ✅ 清空选择后显示0条
5. ✅ 大量数据时性能正常

### 浏览器兼容性
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## 更新日志

### v1.0.0 (2025-01-22)
- 🎉 初始版本发布
- ✨ 支持分页区域显示勾选数量
- ✨ 与CRUD组件完美集成
- ✨ 创建演示页面

---

**相关文件**:
- `src/components/common/container/index.vue` - Container组件主文件
- `src/components/common/crud/index.vue` - CRUD组件主文件  
- `src/views/modules/demo/components/container/checked-rows-demo.vue` - 演示页面
