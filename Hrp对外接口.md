中江县人民智慧财务管理系统接口文档

V1.0.0

2025年7月

## 第1章 接口概述

中江县人民医院作为区域性综合医院，承担着为广大患者提供优质医疗服务的重要职责。随着医院信息化建设的深入推进，医院已建成了包括HIS系统、财务管理系统、物资管理系统、采购管理系统等在内的完整信息化体系。为了实现各系统间的数据互通、业务协同和流程优化，医院构建了统一的中间层服务平台和对外接口体系。

## 系统接口概览

## 接口内容

```mermaid
graph TB
    %% 第三方系统层
    subgraph "第三方系统层"
        HIS["卫林HIS系统<br/>(SQL Server)"]
        SPD["SPD供应链系统<br/>(Oracle)"]
        UF["用友财务系统<br/>(SQL Server)"]
    end

    %% 中间层服务平台
    subgraph "中间层服务平台"
        subgraph "服务组件"
            SYNC["第三方同步<br/>服务"]
            BIZ["业务服务<br/>服务"]
            UF_INT["用友集成服务"]
        end
    end

    %% 医院内部系统层
    subgraph "医院内部系统层"
        MMIS["物资管理系统<br/>(MMIS)"]
        PURMS["采购管理系统<br/>(PURMS)"]
        HRM["人力资源系统<br/>(HRM)"]
        BMS["预算管理系统<br/>(BMS)"]
        ECS["费用控制系统<br/>(ECS)"]
        ERP["财务核算系统<br/>(ERP)"]
    end

    %% 连接关系
    HIS --> SYNC
    SPD --> SYNC
    UF --> UF_INT

    SYNC --> MMIS
    SYNC --> ECS
    BIZ --> MMIS
    BIZ --> PURMS
    UF_INT --> ECS
    UF_INT --> BMS
    UF_INT --> ERP

    %% 系统间关系
    MMIS --> PURMS
    HRM --> BMS
    ECS --> ERP

    %% 样式定义
    classDef thirdParty fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef middleware fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef internal fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class HIS,SPD,UF thirdParty
    class SYNC,BIZ,UF_INT middleware
    class MMIS,PURMS,HRM,BMS,ECS,ERP internal
```

### 系统架构说明

#### 第三方系统层（蓝色）
- **卫林HIS系统** (SQL Server) - 医院信息系统，提供患者信息和医疗数据
- **SPD供应链系统** (Oracle) - 供应链管理系统，提供物资供应链数据
- **用友财务系统** (SQL Server) - 财务管理系统，提供财务核算和预算数据

#### 中间层服务平台（紫色）
- **第三方同步服务** - 负责与外部系统的数据同步和接口对接
- **业务服务服务** - 处理内部业务逻辑和系统间协调
- **用友集成服务** - 专门处理与用友财务系统的集成和数据交换

#### 医院内部系统层（绿色）
- **物资管理系统 (MMIS)** - 负责医院物资的入库、出库、库存管理
- **采购管理系统 (PURMS)** - 负责医院物资采购申请、审核、执行管理
- **人力资源系统 (HRM)** - 负责员工信息、组织架构、工资管理
- **预算管理系统 (BMS)** - 负责预算编制、执行、分析和控制
- **费用控制系统 (ECS)** - 负责费用报销、发票管理、成本控制
- **财务核算系统 (ERP)** - 负责财务凭证、会计核算、财务报表

### 3.1数据接口列表

| **系统** | **接口编号** | **接口地址** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- | --- | --- |
| **HRM** | /   | /hrmOrg/queryOrg | 查询组织架构 | HTTP POST | 查询组织架构树状结构数据 |
| **BMS** | /   | /yy/queryActigInfo | 查询预算执行 | HTTP POST | 通过中间层查询预算执行情况和分析数据 |
| **ECS** | /   | /outpatient/feeDetail | 门诊费用明细查询 | HTTP POST | 查询门诊费用明细数据（待开发接口） |
| **ECS** | /   | /inpatient/feeDetail | 住院费用明细查询 | HTTP POST | 查询住院费用明细数据（待开发接口） |
| **ECS** | /   | /sync/drugSync | 药品入库单同步 | HTTP POST | 通过中间层同步卫林HIS系统药品入库数据 |
| **ECS** | /   | /ecsSatmat/sanMatSync | 卫材入库单同步 | HTTP POST | 直连SPD系统同步卫材入库数据 |
| **ERP** | /   | /employeeSalary/mySalary | 工资数据查询 | HTTP POST | 通过HRM系统查询员工工资数据 |
| **MMIS** | M1001 | /externalApi/gateway | 获取物资分类 | HTTP POST | 获取系统中所有物资分类信息 |
| **MMIS** | M1002 | /externalApi/gateway | 获取物资项目 | HTTP POST | 获取系统中所有物资项目信息 |
| **MMIS** | M1005 | /externalApi/gateway | 查询出库备份记录 | HTTP POST | 查询出库操作的备份记录 |
| **MMIS** | M1006 | /externalApi/gateway | 查询物资出库记录 | HTTP POST | 查询物资出库申请记录 |
| **MMIS** | M1008 | /externalApi/gateway | 查询预出库记录列表 | HTTP POST | 查询预出库记录列表（开发中） |
| **MMIS** | M1009 | /externalApi/gateway | 获取待确认出库工单列表 | HTTP POST | 获取需要确认的出库工单 |
| **MMIS** | M1010 | /externalApi/gateway | 获取可撤销工单列表 | HTTP POST | 获取可以撤销的出库工单 |
| **PURMS** | PUR1001 | /externalApi/gateway | 获取二级库采购单详情 | HTTP POST | 获取二级库物资采购单明细 |
| **PURMS** | PUR1002 | /externalApi/gateway | 获取采购分类 | HTTP POST | 获取所有采购分类信息 |
| **PURMS** | PUR1003 | /externalApi/gateway | 获取采购项目 | HTTP POST | 获取所有采购项目信息 |
| **PURMS** | PUR1005 | /externalApi/gateway | 查询采购申请状态 | HTTP POST | 查询指定采购申请的状态 |
| **PURMS** | PUR1006 | /externalApi/gateway | 查询采购执行状态 | HTTP POST | 查询指定采购申请的执行状态 |


### 3.2服务接口列表

| **系统** | **接口编号** | **接口地址** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- | --- | --- |
| **HRM** | /   | 定时任务 | 推送员工数据 | 定时任务 | 向卫宁系统推送员工信息 |
| **HRM** | /   | 定时任务 | 推送组织数据 | 定时任务 | 向卫宁系统推送组织架构 |
| **BMS** | /   | /bmsOrg/queryOrgByEmpCode | 查询组织信息 | HTTP POST | 根据员工编码查询预算组织信息 |
| **ECS** | /   | /sync/drugSync | 药品入库单同步 | HTTP POST | 通过中间层同步卫林HIS系统药品入库数据 |
| **ECS** | /   | /ecsSatmat/sanMatSync | 卫材入库单同步 | HTTP POST | 直连SPD系统同步卫材入库数据 |
| **ECS** | /   | /yy/econFunSync | 经济功能科目同步 | HTTP POST | 通过中间层同步用友系统经济功能科目 |
| **ECS** | /   | /yy/corrsInsSync | 往来单位同步 | HTTP POST | 通过中间层同步用友系统往来单位信息 |
| **ECS** | /   | /yy/itemSync | 项目信息同步 | HTTP POST | 通过中间层同步用友系统项目信息 |
| **ECS** | /   | /yy/asstCashSync | 现金流量同步 | HTTP POST | 通过中间层同步用友系统现金流量科目 |
| **ECS** | /   | /yy/actigSync | 会计科目同步 | HTTP POST | 通过中间层同步用友系统会计科目 |
| **ERP** | /   | /yy/* | 用友数据同步 | HTTP POST | 通过中间层同步用友系统基础数据 |
| **MMIS** | M1003 | /externalApi/gateway | 确认预出库 | HTTP POST | 确认预出库单据，完成出库流程 |
| **MMIS** | M1004 | /externalApi/gateway | 物资出库撤销 | HTTP POST | 撤销已完成的出库单据 |
| **MMIS** | M1007 | /externalApi/gateway | 物资预出库 | HTTP POST | 创建预出库单据，进行库存检查 |
| **PURMS** | PUR1004 | /externalApi/gateway | 提交采购申请 | HTTP POST | 提交物资采购申请单据 |


## 第4章 接口地址与调用

1、各系统接口地址详见对应章节说明。

2、具体业务请按对应的接口编号进行调用以及传入相应参数。

3、接口输入参数可参考“调用示例代码”。
4、**加密秘钥申请**：MMIS和PURMS系统接口需要使用encryptKey进行认证，请联系我们申请获取。

## 第5章 HRM人力资源接口部分

### 5.1 接口地址

#### 5.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/hrm
- **内网地址**：https://10.2.233.11:8090/back/hrm

#### 5.1.2 具体接口地址
- **查询组织架构**：{BASE_URL}/hrm/hrmOrg/queryOrg
- **查询员工工资**：{BASE_URL}/hrm/employeeSalary/mySalary

### 5.2 修订记录

| **序号** | **版本号** | **日期** | **更改内容** | **修订人** | **审核人** |
| --- | --- | --- | --- | --- | --- |
| 1   | V1.0.0 | 2025/08/01 | 初始版本，对目前已实现的接口进行说明，包括查询组织架构等接口。门诊费用明细查询和住院费用明细查询接口尚未实现。 | 系统管理员 | 系统管理员 |

### 5.3 业务流程图

```mermaid
graph TD
    A[外部系统] --> B[HRM人力资源系统]
    B --> C[组织架构管理]
    B --> D[员工工资管理]
    B --> E[卫宁数据推送]

    C --> C1[查询医院组织架构]
    C --> C2[获取科室部门信息]
    C --> C3[获取人员层级关系]

    D --> D1[查询员工工资条]
    D --> D2[获取薪资构成明细]
    D --> D3[工资统计报表]

    E --> E1[获取OID唯一标识]
    E --> E2[推送员工档案数据]
    E --> E3[推送组织架构数据]

    E1 --> F[卫宁主数据管理系统]
    E2 --> F
    E3 --> F

    C1 --> G[人力资源报告]
    D1 --> H[薪资管理报告]
    F --> I[数据同步确认]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style F fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#fff8e1
    style I fill:#fce4ec
```

### 5.4 接口内容

#### 5.4.1 数据接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- |
| / | 查询组织架构 | HTTP POST | 查询组织架构树状结构数据 |
| / | 查询员工信息 | HTTP POST | 根据员工编码查询员工基本信息 |

#### 5.3.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- |
| / | 推送员工数据 | 定时任务 | 向卫宁系统推送员工基本信息(每天20:00自动执行) |
| / | 推送组织数据 | 定时任务 | 向卫宁系统推送组织机构信息(每天20:00自动执行) |

### 5.5 接口地址与调用

1、卫宁接口地址：http://10.2.231.26:1210/mdm/public/，具体地址在对接后现场通知。

2、HRM接口地址：/hrm/hrmOrg/，具体地址根据系统部署环境确定。

3、具体业务请按对应的接口编号进行调用以及传入相应参数。

4、接口输入参数可参考"调用示例代码"。

### 5.6 卫宁数据推送接口部分

### 5.6.1 / 流程说明

卫宁数据推送接口用于向卫宁主数据管理软件推送员工和组织机构数据，包括获取OID和数据注册两个步骤。

### 5.6.2 / 接口输入报文格式定义

报文采用JSON格式，接口参数定义如下：

**接口输入参数定义**

| **序号** | **参数名称** | **参数说明** | **类型** | **长度** | **是否必填** | **备注** |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | hospitalId | 医院标识 | String | 50 | Y | 医院唯一标识符 |
| 2   | deptCode | 科室编码 | String | 20 | N | 查询指定科室，为空则查询全部 |
| 3   | includeDisabled | 包含禁用项 | Boolean | - | N | 是否包含已禁用的组织，默认false |

### 5.6.3 / 接口输出报文格式定义

报文采用JSON格式，接口参数定义如下：

**接口输出参数定义**

| **序号** | **参数名称** | **参数说明** | **类型** | **长度** | **是否非空** | **备注** |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | orgId | 组织ID | String | 32 | Y | 组织唯一标识符 |
| 2   | orgName | 组织名称 | String | 100 | Y | 组织机构名称 |
| 3   | orgCode | 组织编码 | String | 50 | Y | 组织机构编码 |
| 4   | parentId | 父级组织ID | String | 32 | N | 上级组织标识，根节点为null |
| 5   | orgLevel | 组织层级 | Integer | - | Y | 组织层级深度 |
| 6   | orgType | 组织类型 | String | 20 | Y | 组织类型标识 |
| 7   | isEnabled | 是否启用 | Boolean | - | Y | 组织状态，true为启用 |

### 5.6.4 获取OID接口

#### 5.6.4.1 接口说明

从卫宁主数据管理软件获取唯一标识符(OID)，用于后续数据注册时的唯一标识。

#### 5.4.4.2 输入

##### 5.4.4.2.1 接口基本信息

| 项目  | 内容  |
| --- | --- |
| **接口名称** | 获取OID接口 |
| **接口描述** | 从卫宁主数据管理软件获取唯一标识符(OID) |
| **请求方式** | POST |
| **请求URL** | http://10.2.231.26:1210/mdm/public/getOID |
| **Content-Type** | application/json |

##### 5.4.4.2.2 输入报文格式

###### 4.4.2.2.1 请求头字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | Version | 版本号 | String | 10  | 是   | 固定值：1.0 |
| 2   | LicId | 授权码 | String | 10  | 是   | 固定值：MDM |
| 3   | TranCode | 事务代码 | String | 50  | 是   | 员工：1.2.156.112604.1.2.1702.20  <br>组织：1.2.156.112604.1.2.1702.49 |
| 4   | ServiceVersion | 主数据版本号 | String | 10  | 是   | 固定值：6.0 |
| 5   | ContentType | 内容类型 | String | 10  | 是   | 固定值：JSON |
| 6   | MessageId | 消息ID | String | 50  | 是   | UUID格式 |
| 7   | Timestamp | 时间戳 | String | 30  | 是   | 格式：yyyy-MM-dd HH:mm:ss.SSS |

###### 4.4.2.2.2 请求体字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | size | 获取数量 | Integer | \-  | 是   | 需要获取的OID数量 |
| 2   | hospitalOid | 医院OID | String | 50  | 是   | 固定值：1.2.156.112604.1.7.1660 |
| 3   | modelCode | 模型代码 | String | 20  | 是   | EMPLOYEE：员工  <br>ORGANIZATION：组织机构 |

#### 4.4.3 输出

##### 4.4.3.1 输出报文格式

###### 4.4.3.1.1 响应头字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否非空 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | Version | 版本号 | String | 10  | 是   | 返回请求的版本号 |
| 2   | TranCode | 事务代码 | String | 50  | 是   | 返回请求的事务代码 |
| 3   | MessageId | 消息ID | String | 50  | 是   | 返回请求的消息ID |
| 4   | Timestamp | 时间戳 | String | 30  | 是   | 服务器处理时间 |
| 5   | AckCode | 结果代码 | String | 10  | 是   | 参见状态码说明 |
| 6   | AckMessage | 结果描述 | String | 200 | 否   | 错误信息或成功描述 |

###### 4.4.3.1.2 响应体字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否非空 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | DataItem | 数据项 | Array | \-  | 是   | 生成的OID列表 |

#### 4.4.4 请求示例

```json
{
  "Request": {
    "Head": {
      "Version": "1.0",
      "LicId": "MDM",
      "TranCode": "1.2.156.112604.1.2.1702.20",
      "ServiceVersion": "6.0",
      "ContentType": "JSON",
      "MessageId": "550e8400-e29b-41d4-a716-446655440000",
      "Timestamp": "2025-01-15 10:30:25.123"
    },
    "Body": {
      "DataItem": {
        "size": 1,
        "hospitalOid": "1.2.156.112604.1.7.1660",
        "modelCode": "EMPLOYEE"
      }
    }
  }
}
```

#### 4.4.5 响应示例

```json
{
  "Response": {
    "Head": {
      "Version": "1.0",
      "TranCode": "1.2.156.112604.1.2.1702.20",
      "MessageId": "550e8400-e29b-41d4-a716-446655440000",
      "Timestamp": "2025-01-15 10:30:25.456",
      "AckCode": "100",
      "AckMessage": "成功"
    },
    "Body": {
      "DataItem": [
        "1.2.156.112604.1.7.1660.20250115001"
      ]
    }
  }
}
```

### 5.4.5 数据注册接口

#### 5.4.5.1 接口说明

向卫宁主数据管理软件推送员工或组织机构数据，实现数据同步。

#### 5.4.5.2 输入

##### 5.4.5.2.1 接口基本信息

| 项目  | 内容  |
| --- | --- |
| **接口名称** | 数据注册接口 |
| **接口描述** | 向卫宁主数据管理软件推送员工或组织机构数据 |
| **请求方式** | POST |
| **请求URL** | http://10.2.231.26:1210/mdm/public/register |
| **Content-Type** | application/json |

#### 4.5.2.2 员工数据注册

##### 4.5.2.2.1 输入报文格式 - 请求头

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | Version | 版本号 | String | 10  | 是   | 固定值：1.0 |
| 2   | LicId | 授权码 | String | 10  | 是   | 固定值：MDM |
| 3   | TranCode | 事务代码 | String | 50  | 是   | 员工：1.2.156.112604.1.2.1702.20 |
| 4   | ServiceVersion | 主数据版本号 | String | 10  | 是   | 固定值：6.0 |
| 5   | ContentType | 内容类型 | String | 10  | 是   | 固定值：JSON |
| 6   | MessageId | 消息ID | String | 50  | 是   | UUID格式 |
| 7   | Timestamp | 时间戳 | String | 30  | 是   | 格式：yyyy-MM-dd HH:mm:ss.SSS |

#### 5.4.2.2 输入报文格式 - 员工数据体

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | oid | 员工OID | String | 50  | 是   | 通过获取OID接口获得 |
| 2   | name | 员工姓名 | String | 50  | 是   | 员工真实姓名 |
| 3   | code | 员工编码 | String | 20  | 是   | 员工唯一编码 |
| 4   | status | 状态  | String | 1   | 是   | 1：有效，0：无效 |
| 5   | hospitalOid | 医院OID | String | 50  | 是   | 固定值：1.2.156.112604.1.7.1660 |
| 6   | orgOid | 组织机构OID | String | 50  | 是   | 员工所属科室OID |
| 7   | extendedAttributes | 扩展属性 | Array | \-  | 否   | 自定义属性列表 |

#### 5.4.2.3 扩展属性字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | name | 属性名称 | String | 50  | 是   | 扩展属性的名称 |
| 2   | value | 属性值 | String | 200 | 是   | 扩展属性的值 |

### 5.4.3 组织机构数据注册

#### 5.4.3.1 输入报文格式 - 请求头

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | Version | 版本号 | String | 10  | 是   | 固定值：1.0 |
| 2   | LicId | 授权码 | String | 10  | 是   | 固定值：MDM |
| 3   | TranCode | 事务代码 | String | 50  | 是   | 组织：1.2.156.112604.1.2.1702.49 |
| 4   | ServiceVersion | 主数据版本号 | String | 10  | 是   | 固定值：6.0 |
| 5   | ContentType | 内容类型 | String | 10  | 是   | 固定值：JSON |
| 6   | MessageId | 消息ID | String | 50  | 是   | UUID格式 |
| 7   | Timestamp | 时间戳 | String | 30  | 是   | 格式：yyyy-MM-dd HH:mm:ss.SSS |

#### 5.4.3.2 输入报文格式 - 组织机构数据体

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否必填 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | oid | 组织机构OID | String | 50  | 是   | 通过获取OID接口获得 |
| 2   | name | 组织机构名称 | String | 100 | 是   | 科室或部门名称 |
| 3   | code | 组织机构编码 | String | 20  | 是   | 科室唯一编码 |
| 4   | status | 状态  | String | 1   | 是   | 1：有效，0：无效 |
| 5   | hospitalOid | 医院OID | String | 50  | 是   | 固定值：1.2.156.112604.1.7.1660 |
| 6   | parentOid | 上级组织OID | String | 50  | 否   | 上级科室OID，根节点可为空 |
| 7   | extendedAttributes | 扩展属性 | Array | \-  | 否   | 自定义属性列表 |

### 5.4.4 输出报文格式

#### 5.4.4.1 响应头字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否非空 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | Version | 版本号 | String | 10  | 是   | 返回请求的版本号 |
| 2   | TranCode | 事务代码 | String | 50  | 是   | 返回请求的事务代码 |
| 3   | MessageId | 消息ID | String | 50  | 是   | 返回请求的消息ID |
| 4   | Timestamp | 时间戳 | String | 30  | 是   | 服务器处理时间 |
| 5   | AckCode | 结果代码 | String | 10  | 是   | 参见状态码说明 |
| 6   | AckMessage | 结果描述 | String | 200 | 否   | 错误信息或成功描述 |

#### 5.4.4.2 响应体字段说明

| 序号  | 数据元标识 | 数据元名称 | 类型  | 长度  | 是否非空 | 备注  |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | DataItem | 数据项 | Object | \-  | 是   | 注册结果信息，通常为空对象 |

### 5.4.5 员工数据注册请求示例

{
"Request": {
"Head": {
"Version": "1.0",
"LicId": "MDM",
"TranCode": "1.2.156.112604.1.2.1702.20",
"ServiceVersion": "6.0",
"ContentType": "JSON",
"MessageId": "550e8400-e29b-41d4-a716-446655440001",
"Timestamp": "2025-01-15 10:35:25.123"
},
"Body": {
"DataItem": {
"oid": "1.2.156.112604.1.7.1660.20250115001",
"name": "张三",
"code": "EMP001",
"status": "1",
"hospitalOid": "1.2.156.112604.1.7.1660",
"orgOid": "1.2.156.112604.1.7.1660.ORG001",
"extendedAttributes": \[
{
"name": "职位",
"value": "主治医师"
},
{
"name": "联系电话",
"value": "13800138000"
}
\]
}
}
}
}

### 5.4.6 组织机构数据注册请求示例

{
"Request": {
"Head": {
"Version": "1.0",
"LicId": "MDM",
"TranCode": "1.2.156.112604.1.2.1702.49",
"ServiceVersion": "6.0",
"ContentType": "JSON",
"MessageId": "550e8400-e29b-41d4-a716-446655440002",
"Timestamp": "2025-01-15 10:40:25.123"
},
"Body": {
"DataItem": {
"oid": "1.2.156.112604.1.7.1660.ORG001",
"name": "内科",
"code": "NK001",
"status": "1",
"hospitalOid": "1.2.156.112604.1.7.1660",
"parentOid": "1.2.156.112604.1.7.1660.ROOT",
"extendedAttributes": \[
{
"name": "科室类型",
"value": "临床科室"
}
\]
}
}
}
}

### 5.4.7 响应示例

{
"Response": {
"Head": {
"Version": "1.0",
"TranCode": "1.2.156.112604.1.2.1702.20",
"MessageId": "550e8400-e29b-41d4-a716-446655440001",
"Timestamp": "2025-01-15 10:35:25.456",
"AckCode": "100",
"AckMessage": "成功"
},
"Body": {
"DataItem": {}
}
}
}

## 第6章 BMS预算管理接口部分

### 6.1 接口地址

#### 6.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/bms
- **内网地址**：https://10.2.233.11:8090/back/bms

#### 6.1.2 具体接口地址
- **查询预算执行**：http://172.16.252.24:9527/mid/yy/queryActigInfo（通过中间层）
- **查询组织信息**：{BASE_URL}/bms/bmsOrg/queryOrgByEmpCode

### 6.2 业务流程图

```mermaid
graph TD
    A[外部系统] --> B[BMS预算管理系统]
    B --> C[预算执行查询]

    C --> C1[按部门查询预算]
    C --> C2[按时间范围查询]
    C --> C3[按预算类型查询]

    C1 --> D[预算执行分析]
    C2 --> D
    C3 --> D

    D --> D1[预算金额统计]
    D --> D2[执行金额统计]
    D --> D3[执行率计算]
    D --> D4[剩余预算分析]

    D1 --> E[预算执行报告]
    D2 --> E
    D3 --> E
    D4 --> E

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style E fill:#fff3e0
```

### 6.3 接口内容

### 6.3.1 数据接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- |
| / | 查询预算执行 | HTTP POST | 通过中间层查询预算执行情况和分析数据 |

#### 6.1.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** |
| --- | --- | --- | --- |
| / | 查询组织信息 | HTTP POST | 根据员工编码查询预算组织信息 |

### 6.2 接口地址与调用

#### 6.2.1 查询预算执行接口
- **接口地址**：`/yy/queryActigInfo` (通过中间层调用)
- **调用方式**：HTTP POST
- **说明**：BMS系统通过中间层提供预算执行查询服务

#### 6.2.2 查询组织信息接口
- **接口地址**：`/bmsOrg/queryOrgByEmpCode` (BMS内部接口)
- **调用方式**：HTTP POST
- **说明**：根据员工编码查询预算组织信息

## 第6.3 预算执行接口部分

### 6.3.1 流程说明

预算执行接口用于查询医院各部门、科室的预算执行情况，包括预算金额、实际执行金额、执行进度等关键指标。

### 6.3.2 接口输入报文格式定义

报文采用JSON格式，接口参数定义如下：

**接口输入参数定义**

| **序号** | **参数名称** | **参数说明** | **类型** | **长度** | **是否必填** | **备注** |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | hospitalId | 医院ID | String | 32  | Y   | 医院唯一标识 |
| 2   | deptId | 部门ID | String | 32  | N   | 部门唯一标识，为空时查询全院 |
| 3   | budgetYear | 预算年度 | String | 4   | Y   | 格式：yyyy |
| 4   | budgetMonth | 预算月份 | String | 2   | N   | 格式：MM，为空时查询全年 |
| 5   | budgetType | 预算类型 | String | 10  | N   | 收入预算/支出预算 |
| 6   | pageNum | 页码  | Integer | 10  | N   | 默认为1 |
| 7   | pageSize | 每页大小 | Integer | 10  | N   | 默认为20 |

### 6.3.3 接口输出报文格式定义

报文采用JSON格式，接口参数定义如下：

**接口输出参数定义**

| **序号** | **参数名称** | **参数说明** | **类型** | **长度** | **是否非空** | **备注** |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | deptId | 部门ID | String | 32  | Y   | 部门唯一标识 |
| 2   | deptName | 部门名称 | String | 100 | Y   | 部门名称 |
| 3   | budgetAmount | 预算金额 | Decimal | -   | Y   | 预算总金额 |
| 4   | executedAmount | 执行金额 | Decimal | -   | Y   | 已执行金额 |
| 5   | remainingAmount | 剩余金额 | Decimal | -   | Y   | 剩余预算金额 |
| 6   | executionRate | 执行率 | Decimal | -   | Y   | 预算执行百分比 |
| 7   | budgetYear | 预算年度 | String | 4   | Y   | 预算年度 |

### 6.3.4 查询预算执行接口

#### 6.3.4.1 接口说明

通过中间层调用用友系统，查询预算执行情况的实时数据，支持按年度、经济科目、会计科目等维度进行查询统计。

**接口路径**：`/yy/queryActigInfo`
**调用方式**：HTTP POST
**实现类**：`BudgetResultReadService.queryActigInfo()`

#### 6.3.4.2 输入

##### 6.3.4.2.1 基本信息

节点标识：queryActigInfo

**请求参数**

| **序号** | **参数代码** | **参数名称** | **参数类型** | **参数长度** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- | --- | --- |
| 1   | year | 查询年度 | 字符型 | 4   | Y   | 查询的预算年度，格式：yyyy |
| 2   | month | 查询月份 | 字符型 | 2   | N   | 查询的月份，格式：MM |
| 3   | yyBudgetInfoType | 查询类型 | 字符型 | 1   | Y   | 1:总发生额 2:经济科目明细 3:会计科目明细 |
| 4   | econCodeStr | 经济科目代码 | 字符型 | 500 | N   | 经济科目代码字符串，多个用逗号分隔 |
| 5   | actigCodeStr | 会计科目代码 | 字符型 | 500 | N   | 会计科目代码字符串，多个用逗号分隔 |
| 6   | deptCodeStr | 部门代码 | 字符型 | 500 | N   | 部门代码字符串，多个用逗号分隔 |

#### 6.3.4.3 输出

##### 6.3.4.3.1 预算执行信息

节点标识：queryActigInfo

**响应数据结构**

| **序号** | **参数代码** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | code | 响应状态码 | 数值型 | 200表示成功 |
| 2   | message | 响应消息 | 字符型 | 成功或错误信息 |
| 3   | data | 响应数据 | Object | 根据查询类型返回不同结构 |

**data字段结构（根据yyBudgetInfoType不同）**：

**类型1（总发生额）**：
- `totalActualAmount`: 总实际发生金额
- `econDetails`: 经济科目明细列表
- `actigDetails`: 会计科目明细列表

**类型2/3（明细数据）**：
- `deptCode`: 部门代码
- `deptName`: 部门名称
- `econSubCode`: 经济科目代码
- `actigSubCode`: 会计科目代码
- `actualAmount`: 实际发生金额
- `year`: 年度
- `month`: 月份

### 6.3.5 调用示例

#### 6.3.5.1 请求示例

```json
{
  "year": "2025",
  "month": "01",
  "yyBudgetInfoType": "1",
  "econCodeStr": "30211,30216",
  "actigCodeStr": "",
  "deptCodeStr": "001,002"
}
```

#### 6.3.5.2 响应示例

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalActualAmount": 1250000.00,
    "econDetails": [
      {
        "deptCode": "001",
        "deptName": "内科",
        "econSubCode": "30211",
        "actualAmount": 750000.00,
        "year": "2025",
        "month": "01"
      }
    ],
    "actigDetails": [
      {
        "deptCode": "001",
        "deptName": "内科",
        "actigSubCode": "51010403",
        "actualAmount": 500000.00,
        "year": "2025",
        "month": "01"
      }
    ]
  }
}
```

### 6.3.6 查询组织信息接口

#### 6.3.6.1 接口说明

根据员工编码查询预算组织信息，用于获取员工所属的预算组织架构。

**接口路径**：`/bms/bmsOrg/queryOrgByEmpCode`
**调用方式**：HTTP POST
**实现类**：`BmsOrgReadService.queryOrgByEmpCode()`

#### 6.3.6.2 请求参数

| **序号** | **参数代码** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- | --- |
| 1   | empCode | 员工编码 | 字符型 | Y   | 员工唯一编码 |

#### 6.3.6.3 响应数据

| **序号** | **参数代码** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | orgId | 组织ID | 字符型 | 组织唯一标识 |
| 2   | orgName | 组织名称 | 字符型 | 组织名称 |
| 3   | orgCode | 组织编码 | 字符型 | 组织编码 |
| 4   | parentOrgId | 上级组织ID | 字符型 | 上级组织标识 |
| 5   | orgLevel | 组织层级 | 数值型 | 组织层级 |

#### 6.3.6.4 调用示例

**请求示例**：
```json
{
  "empCode": "E001"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "orgId": "ORG001",
    "orgName": "内科",
    "orgCode": "NK001",
    "parentOrgId": "ORG000",
    "orgLevel": 2
  }
}
```

## 第6.4 注意事项

### 6.4.1 数据格式要求

- **时间格式**：严格按照 yyyy-MM-dd HH:mm:ss 格式
- **金额格式**：使用数值型，保留两位小数
- **字符编码**：统一使用UTF-8编码
- **JSON格式**：严格按照JSON规范

### 6.4.2 业务规则

- **数据权限**：只能查询当前用户有权限的部门数据
- **时间范围**：查询时间范围不能超过5年



## 第7章 ECS费用报销系统接口

### 7.1 接口地址

#### 7.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/ecs
- **内网地址**：https://10.2.233.11:8090/back/ecs

#### 7.1.2 具体接口地址
- **门诊费用明细查询**：{BASE_URL}/ecs/outpatient/feeDetail（待开发，具体地址在对接后现场通知）
- **住院费用明细查询**：{BASE_URL}/ecs/inpatient/feeDetail（待开发，具体地址在对接后现场通知）
- **药品入库单同步**：http://172.16.252.24:9527/mid/sync/drugSync（通过中间层）
- **卫材入库单同步**：{BASE_URL}/ecs/ecsSatmat/sanMatSync
- **用友数据同步**：http://172.16.252.24:9527/mid/yy/*（通过中间层）

### 7.2 业务流程图

```mermaid
graph TD
    A[外部系统] --> B[ECS费用报销系统]
    B --> C[门诊费用查询]
    B --> D[住院费用查询]

    C --> C1[门诊费用明细]
    C --> C2[门诊收费项目]
    C --> C3[门诊支付信息]

    D --> D1[住院费用明细]
    D --> D2[住院收费项目]
    D --> D3[住院结算信息]

    C1 --> E[费用统计分析]
    C2 --> E
    C3 --> E
    D1 --> E
    D2 --> E
    D3 --> E

    E --> F[费用报销单据]
    E --> G[费用明细报告]
    E --> H[收费项目汇总]

    style A fill:#e1f5fe
    style B fill:#fce4ec
    style E fill:#fff8e1
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#f3e5f5
```

### 7.3 接口内容

### 7.2.1 ECS系统接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** | **状态** |
| --- | --- | --- | --- | --- | --- |
| /   | 门诊费用明细查询 | HTTP POST | 查询门诊费用明细数据，支持按时间、科室等条件查询 | HIS系统 | 待开发 |
| /   | 住院费用明细查询 | HTTP POST | 查询住院费用明细数据，支持按时间、病区等条件查询 | HIS系统 | 待开发 |
| /   | 药品入库单同步 | HTTP POST | 通过中间层同步卫林HIS系统药品入库数据 | ECS系统 | 已实现 |
| /   | 卫材入库单同步 | HTTP POST | 直连SPD系统同步卫材入库数据 | ECS系统 | 已实现 |

#### 7.1.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** |
| --- | --- | --- | --- | --- |
| /   | 经济功能科目同步 | HTTP POST | 通过中间层同步用友系统经济功能科目 | ECS系统 |
| /   | 往来单位同步 | HTTP POST | 通过中间层同步用友系统往来单位信息 | ECS系统 |
| /   | 项目信息同步 | HTTP POST | 通过中间层同步用友系统项目信息 | ECS系统 |
| /   | 现金流量同步 | HTTP POST | 通过中间层同步用友系统现金流量科目 | ECS系统 |
| /   | 会计科目同步 | HTTP POST | 通过中间层同步用友系统会计科目 | ECS系统 |

## 第7.3 接口地址与调用

### 7.3.1 基础配置

**服务器地址**：http://172.16.252.24:9527

**接口前缀**：/mid

### 7.2.2 接口URL映射

根据Nacos配置，各接口的完整URL如下：

| **功能模块** | **接口路径** | **完整URL** | **调用方** | **状态** |
| --- | --- | --- | --- | --- | --- |
| 门诊费用明细查询 | /outpatient/feeDetail | http://172.16.252.24:9527/mid/outpatient/feeDetail | HIS系统 | 待开发 |
| 住院费用明细查询 | /inpatient/feeDetail | http://172.16.252.24:9527/mid/inpatient/feeDetail | HIS系统 | 待开发 |
| 药品入库单同步 | /sync/drugSync | http://172.16.252.24:9527/mid/sync/drugSync | ECS系统 | 已实现 |
| 卫材入库单同步 | /ecsSatmat/sanMatSync | http://ECS系统地址/ecsSatmat/sanMatSync | ECS系统 | 已实现 |
| 经济功能科目同步 | /yy/econFunSync | http://172.16.252.24:9527/mid/yy/econFunSync | ECS系统 |
| 往来单位同步 | /yy/corrsInsSync | http://172.16.252.24:9527/mid/yy/corrsInsSync | ECS系统 |
| 项目信息同步 | /yy/itemSync | http://172.16.252.24:9527/mid/yy/itemSync | ECS系统 |
| 现金流量同步 | /yy/asstCashSync | http://172.16.252.24:9527/mid/yy/asstCashSync | ECS系统 |
| 会计科目同步 | /yy/actigSync | http://172.16.252.24:9527/mid/yy/actigSync | ECS系统 |

### 7.2.3 待开发接口详细规划

#### 7.2.3.1 门诊费用明细接口

**接口路径**：`/ecs/outpatient/feeDetail`
**调用方式**：HTTP POST
**开发状态**：待开发
**业务目标**：获取门诊费用原始数据，包括医保明细、收费员报表、费用明细三个报表数据，组装成门诊费用汇总报表

##### 输入参数（我们发送给对方）

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
|----------|-------------|-------------|-------------|----------|
| 1 | hospitalId | String | Y | 医院ID |
| 2 | startDate | String | Y | 开始日期，格式：yyyy-MM-dd HH:mm:ss |
| 3 | endDate | String | Y | 结束日期，格式：yyyy-MM-dd HH:mm:ss |

##### 输出参数（对方返回给我们）

| **序号** | **参数名称** | **参数类型** | **说明** |
|----------|-------------|-------------|----------|
| 1 | code | Integer | 响应状态码 |
| 2 | message | String | 响应消息 |
| 3 | data | Object | 门诊费用原始数据 |
| 4 | success | Boolean | 是否成功 |

**data 门诊费用原始数据结构**：

| **参数名称** | **参数类型** | **说明** |
|-------------|-------------|----------|
| medicalInsuranceDetails | List | 医保明细表数据 |
| cashierReports | List | 收费员报表数据 |
| feeDetails | Object | 费用明细数据 |

**医保明细表数据结构（medicalInsuranceDetails）**：

| **字段名** | **类型** | **说明** |
|-----------|---------|----------|
| settlementType | String | 清算类别 |
| totalFee | BigDecimal | 费用总额 |
| poolPayment | BigDecimal | 统筹支付 |
| personalAccount | BigDecimal | 个人账户支付 |
| supplementaryMedical | BigDecimal | 补充医疗 |
| civilServantSubsidy | BigDecimal | 公务员补助 |
| seriousIllnessInsurance | BigDecimal | 大病保险 |
| workInjuryInsurance | BigDecimal | 工伤保险 |
| otherInsurance | BigDecimal | 其他保险 |
| hospitalDiscount | BigDecimal | 院内优惠 |
| actualPayment | BigDecimal | 实际支付 |
| medicalAdvance | BigDecimal | 医疗垫付 |
| medicalDifference | BigDecimal | 医保差额 |

**收费员报表数据结构（cashierReports）**：

| **字段名** | **类型** | **说明** |
|-----------|---------|----------|
| operatorName | String | 操作员姓名 |
| cashAmount | BigDecimal | 现金金额 |
| billNumber | String | 帐单序号 |
| refundCount | Integer | 退方数 |
| refundAmount | BigDecimal | 退费总金额 |
| operationCount | Integer | 操作数 |
| paymentCenterAmount | BigDecimal | 支付中心金额 |
| accountAmount | BigDecimal | 记账金额 |
| cardDeductionAmount | BigDecimal | 扣卡金额 |

**费用明细数据结构（feeDetails）**：

| **字段名** | **类型** | **说明** |
|-----------|---------|----------|
| totalAmount | BigDecimal | 总金额 |
| cashPersonal | BigDecimal | 现金个人 |
| checkPersonal | BigDecimal | 支票个人 |
| posPersonal | BigDecimal | POS个人 |
| wechatPublic | BigDecimal | 公众号 |
| paymentCenter | BigDecimal | 支付中心 |
| alipay | BigDecimal | 支付宝 |
| healthDeyang | BigDecimal | 健康德阳 |
| clinicPayment | BigDecimal | 诊间支付 |
| bankMedicalPayment | BigDecimal | 银医通支付 |
| discountAmount | BigDecimal | 优惠金额 |
| arrearsAmount | BigDecimal | 欠款金额 |

##### 数据组装逻辑

**中间层系统组装逻辑**：

1. **收费员金额汇总**：
   - 从收费员报表中提取每个收费员的现金金额
   - 按收费员姓名分组汇总

2. **医保支付汇总**：
   - 从医保明细表中汇总各种支付类型
   - 包括：统筹支付、个人账户支付、补充医疗、公务员补助、大病保险、工伤保险、其他保险、院内优惠、医疗垫付、医保差额

3. **支付渠道汇总**：
   - 从费用明细表中提取各种支付渠道金额
   - 包括：现金个人、支票个人、POS个人、公众号、支付中心、支付宝、健康德阳、诊间支付、银医通支付、优惠金额、欠款金额

4. **门诊收入计算**：
   - 汇总所有收费员现金金额 + 所有支付渠道金额
   - 验证与费用明细表总金额一致性

#### 7.2.3.2 住院费用明细接口

**接口路径**：`/ecs/inpatient/feeDetail`
**调用方式**：HTTP POST
**开发状态**：待开发
**业务目标**：获取住院费用原始数据，包括医保明细、收费员报表、费用明细、支付方式四个报表数据，组装成住院费用汇总报表

##### 输入参数（我们发送给对方）

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
|----------|-------------|-------------|-------------|----------|
| 1 | hospitalId | String | Y | 医院ID |
| 2 | startDate | String | Y | 开始日期，格式：yyyy-MM-dd HH:mm:ss |
| 3 | endDate | String | Y | 结束日期，格式：yyyy-MM-dd HH:mm:ss |

##### 输出参数（对方返回给我们）

| **序号** | **参数名称** | **参数类型** | **说明** |
|----------|-------------|-------------|----------|
| 1 | code | Integer | 响应状态码 |
| 2 | message | String | 响应消息 |
| 3 | data | Object | 住院费用原始数据 |
| 4 | success | Boolean | 是否成功 |

**data 住院费用原始数据结构**：

| **参数名称** | **参数类型** | **说明** |
|-------------|-------------|----------|
| medicalInsuranceDetails | List | 医保明细表数据 |
| cashierReports | List | 收费员报表数据 |
| feeDetails | Object | 费用明细数据 |
| paymentMethods | Object | 支付方式数据 |

**medicalInsuranceDetails 医保明细表数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
|----------|-------------|-------------|-------------|----------|----------|
| 1 | settlementStartTime | String | Y | 清算开始时间，格式：yyyy-MM-dd | "2025-07-13" |
| 2 | settlementEndTime | String | Y | 清算结束时间，格式：yyyy-MM-dd | "2025-07-14" |
| 3 | settlementCenterCode | String | Y | 清算分中心代码 | "1" |
| 4 | settlementCenterName | String | Y | 清算分中心名称 | "按项目" |
| 5 | settlementCategoryCode | String | Y | 清算类别代码 | "21" |
| 6 | settlementCategoryName | String | Y | 清算类别名称 | "住院"、"生育住院"、"跨省异地住院" |
| 7 | totalAmount | BigDecimal | Y | 费用总额，保留2位小数 | 23254.99 |
| 8 | totalPersons | Integer | Y | 总人次 | 4 |
| 9 | personalAccountPayment | BigDecimal | N | 个人账户支付金额，保留2位小数 | 106.00 |
| 10 | basicMedicalPayment | BigDecimal | N | 基本医疗统筹支付金额，保留2位小数 | 12691.38 |
| 11 | civilServantSubsidy | BigDecimal | N | 公务员补助报销金额，保留2位小数 | 2733.10 |
| 12 | supplementaryMedical | BigDecimal | N | 补充医疗报销金额，保留2位小数 | 24.93 |
| 13 | majorIllnessInsurance | BigDecimal | N | 大病保险赔付金额，保留2位小数 | 509.87 |
| 14 | employeeMajorIllness | BigDecimal | N | 职工大病保险赔付金额，保留2位小数 | 0.00 |
| 15 | workInjuryInsurance | BigDecimal | N | 工伤保险报销金额，保留2位小数 | 0.00 |
| 16 | hospitalBurden | BigDecimal | N | 医院负担金额，保留2位小数 | 1088.20 |
| 17 | maternityInsurance | BigDecimal | N | 生育保险赔付金额，保留2位小数 | 0.00 |
| 18 | otherInsurance | BigDecimal | N | 其他保险金额，保留2位小数 | 1390.82 |
| 19 | medicalInsuranceDifference | BigDecimal | N | 医保差额，保留2位小数 | 0.00 |
| 20 | medicalInsuranceExcess | BigDecimal | N | 医保统筹超出部分，保留2位小数 | 0.00 |
| 21 | nationalMedicalInsurance | BigDecimal | N | 国家医保，保留2位小数 | 0.00 |

**说明**：
- 必须包含汇总行数据，用于生成最终的医保支付统计
- 各种支付金额字段如果没有数据则传0.00，不能传null
- 清算类别包括：住院、生育住院、跨省异地住院、城乡居民生育清算、职工DRGS医疗费用清算、居民DRGS医疗费用清算

**cashierReports 收费员报表数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
|----------|-------------|-------------|-------------|----------|----------|
| 1 | operatorName | String | Y | 操作员姓名（收费员姓名） | "刘涛"、"易美琪"、"吕迎春" |
| 2 | billNumber | String | Y | 账单序号 | "29637" |
| 3 | actualCash | BigDecimal | Y | 实缴现金，保留2位小数，用于汇总收费员金额 | 800.00 |
| 4 | settlementSupplement | BigDecimal | N | 结算结补，保留2位小数 | 0.00 |
| 5 | settlementRefund | BigDecimal | N | 结算结退，保留2位小数 | 0.00 |
| 6 | startTime | String | Y | 开始时间，格式：yyyy.MM.dd HH:mm:ss | "2025.07.12 23:55:03" |
| 7 | endTime | String | Y | 结账时间，格式：yyyy.MM.dd HH:mm:ss | "2025.07.13 10:34:41" |
| 8 | receiptCount | Integer | N | 收据张数 | 2 |
| 9 | invoiceCount | Integer | N | 发票张数 | 0 |
| 10 | totalInvoiceAmount | BigDecimal | N | 发票总费用，保留2位小数 | 0.00 |
| 11 | paymentStatus | String | N | 缴款状态 | "未缴款"、"已缴款" |
| 12 | borrowCash | BigDecimal | N | 借现金，保留2位小数 | 0.00 |
| 13 | lendCash | BigDecimal | N | 贷现金，保留2位小数 | 0.00 |
| 14 | borrowCheck | BigDecimal | N | 借支票，保留2位小数 | 0.00 |
| 15 | lendCheck | BigDecimal | N | 贷支票，保留2位小数 | 0.00 |
| 16 | refundInvoiceAmount | BigDecimal | N | 退票总费用，保留2位小数 | 0.00 |

**说明**：
- actualCash字段是关键字段，用于汇总各收费员的现金收入
- 必须包含指定时间范围内所有收费员的记录
- 金额字段如果没有数据则传0.00，不能传null

**feeDetails 费用明细数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
|----------|-------------|-------------|-------------|----------|----------|
| 1 | statisticsPeriod | String | Y | 统计期描述 | "统计期:2025年07月13日~~2025年07月14日" |
| 2 | midwaySettlementCount | Integer | Y | 中途结算人次 | 6 |
| 3 | totalSettlementCount | Integer | Y | 总共结算人次 | 83 |
| 4 | totalAmount | BigDecimal | Y | 总费用（G+B+H+S+Q），保留2位小数，用于数据校验 | 538200.12 |
| 5 | arrearsSettlement | BigDecimal | N | 欠费结算，保留2位小数 | 0.00 |
| 6 | personalPayment | BigDecimal | Y | 个人支付（G），保留2位小数 | 272617.10 |
| 7 | medicalInsurancePayment | BigDecimal | Y | 医保支付（B），保留2位小数 | 265583.02 |
| 8 | hospitalDiscount | BigDecimal | N | 医院优惠（H），保留2位小数 | 0.00 |
| 9 | socialSecurityPayment | BigDecimal | N | 社保支付（S），保留2位小数 | 0.00 |
| 10 | otherPayment | BigDecimal | N | 其他支付（Q），保留2位小数 | 0.00 |
| 11 | rounding | BigDecimal | N | 舍入，保留2位小数 | 0.00 |

**说明**：
- totalAmount用于与其他报表数据进行一致性校验
- personalPayment + medicalInsurancePayment + hospitalDiscount + socialSecurityPayment + otherPayment = totalAmount
- 统计期必须与请求的时间范围一致

**paymentMethods 支付方式数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
|----------|-------------|-------------|-------------|----------|----------|
| 1 | advancePaymentCollection | BigDecimal | Y | 收取预交金（M），保留2位小数 | 338041.80 |
| 2 | advancePaymentCash | BigDecimal | Y | 预交金现金（MX），保留2位小数 | 55491.00 |
| 3 | advancePaymentCheck | BigDecimal | Y | 预交金支票（MZ），保留2位小数 | 282550.80 |
| 4 | qrCodePayment | BigDecimal | N | 扫码付，保留2位小数 | 272150.80 |
| 5 | wechatPayment | BigDecimal | N | 微信支付，保留2位小数 | 10400.00 |
| 6 | alipayPayment | BigDecimal | N | 支付宝支付，保留2位小数 | 0.00 |
| 7 | selfServiceMachine | BigDecimal | N | 自助机支付，保留2位小数 | 32500.00 |
| 8 | settlementRefund | BigDecimal | N | 结算收退款（T），保留2位小数 | 2281.53 |
| 9 | bankDeposit | BigDecimal | N | 银行缴款，保留2位小数 | 340323.33 |
| 10 | settlementAdvanceOffset | BigDecimal | Y | 结算冲低预交款合计，保留2位小数 | 270335.57 |
| 11 | transferPayment | BigDecimal | N | 转账支付，保留2位小数 | 0.00 |
| 12 | checkPayment | BigDecimal | N | 支票支付，保留2位小数 | 0.00 |
| 13 | otherPayment | BigDecimal | N | 其他支付方式，保留2位小数 | 0.00 |

**说明**：
- 支付方式数据用于生成支付渠道统计
- 各支付方式金额如果没有数据则传0.00，不能传null
- advancePaymentCash + advancePaymentCheck = advancePaymentCollection（数据一致性校验）
- 支付方式明细包括：扫码付、微信、支付宝、自助机、转账等具体渠道

##### 数据组装逻辑（我们系统内部处理）

**接收到对方数据后，我们系统的组装逻辑**：

1. **收费员金额汇总**：
   - 从收费员报表中提取每个收费员的实缴现金金额
   - 按收费员姓名分组汇总，生成收费员金额明细

2. **医保支付汇总**：
   - 从医保明细表中汇总各种医保支付类型
   - 包括：个人账户支付、基本医疗统筹支付、公务员补助、补充医疗、大病保险、其他保险、医院负担等

3. **支付方式汇总**：
   - 从支付方式表中提取各种支付渠道金额
   - 包括：预交金现金、预交金支票、扫码付、微信支付、自助机支付、结算收退款等

4. **住院收入计算**：
   - 汇总所有收费员现金金额 + 所有支付渠道金额
   - 验证与费用明细表总金额一致性
   - 生成最终的住院费用汇总报表（"导"表格式）

**对方公司需要提供的数据要求**：
- 必须包含完整的4个报表数据
- 数据时间范围必须与请求的startDate/endDate一致
- 金额字段保留2位小数精度
- 收费员报表必须包含实缴现金字段
- 医保明细表必须包含各种支付类型的汇总数据

## 第7.3 通用接口规范

### 7.3.1 请求格式

所有接口统一使用JSON格式进行数据交互：

{
"参数名1": "参数值1",
"参数名2": "参数值2"
}

### 7.3.2 响应格式

统一使用CommonResult包装响应数据：

{
"code": 200,
"message": "操作成功",
"data": {},
"success": true
}

### 7.3.3 状态码说明

| **状态码** | **说明** | **描述** |
| --- | --- | --- |
| 200 | 成功  | 请求处理成功 |
| 400 | 请求错误 | 请求参数错误或格式不正确 |
| 401 | 未授权 | 身份验证失败 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 接口或资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

## 第7.4 ECS系统接口详细说明

### 7.4.1 【/】门诊费用明细接口

#### 7.4.1.1 接口说明

我们系统调用对方公司此接口获取门诊费用原始数据，包括医保明细、收费员报表、费用明细三个报表数据。我们系统根据时间范围请求数据，对方返回原始数据后，我们负责组装成门诊费用汇总报表。

#### 7.4.1.2 接口路径

**请求方式**：POST

**接口路径**：由对方公司提供（待确定）

#### 7.4.1.3 输入参数（我们发送给对方）

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | startDate | String | Y   | 开始日期，格式：yyyy-MM-dd HH:mm:ss |
| 3   | endDate | String | Y   | 结束日期，格式：yyyy-MM-dd HH:mm:ss |

#### ******* 输出参数（对方返回给我们）

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 门诊费用原始数据 |
| 4   | success | Boolean | 是否成功 |

**data 门诊费用原始数据结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| medicalInsuranceDetails | List | 医保明细表数据 |
| cashierReports | List | 收费员报表数据 |
| feeDetails | Object | 费用明细数据 |

**医保明细表数据结构（medicalInsuranceDetails）**：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| settlementType | String | 清算类别 |
| totalFee | BigDecimal | 费用总额 |
| poolPayment | BigDecimal | 统筹支付 |
| personalAccount | BigDecimal | 个人账户支付 |
| supplementaryMedical | BigDecimal | 补充医疗 |
| civilServantSubsidy | BigDecimal | 公务员补助 |
| seriousIllnessInsurance | BigDecimal | 大病保险 |
| workInjuryInsurance | BigDecimal | 工伤保险 |
| otherInsurance | BigDecimal | 其他保险 |
| hospitalDiscount | BigDecimal | 院内优惠 |
| actualPayment | BigDecimal | 实际支付 |
| medicalAdvance | BigDecimal | 医疗垫付 |
| medicalDifference | BigDecimal | 医保差额 |

**收费员报表数据结构（cashierReports）**：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| operatorName | String | 操作员姓名 |
| cashAmount | BigDecimal | 现金金额 |
| billNumber | String | 帐单序号 |
| refundCount | Integer | 退方数 |
| refundAmount | BigDecimal | 退费总金额 |
| operationCount | Integer | 操作数 |
| paymentCenterAmount | BigDecimal | 支付中心金额 |
| accountAmount | BigDecimal | 记账金额 |
| cardDeductionAmount | BigDecimal | 扣卡金额 |

**费用明细数据结构（feeDetails）**：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| totalAmount | BigDecimal | 总金额 |
| cashPersonal | BigDecimal | 现金个人 |
| checkPersonal | BigDecimal | 支票个人 |
| posPersonal | BigDecimal | POS个人 |
| wechatPublic | BigDecimal | 公众号 |
| paymentCenter | BigDecimal | 支付中心 |
| alipay | BigDecimal | 支付宝 |
| healthDeyang | BigDecimal | 健康德阳 |
| clinicPayment | BigDecimal | 诊间支付 |
| bankMedicalPayment | BigDecimal | 银医通支付 |
| discountAmount | BigDecimal | 优惠金额 |
| arrearsAmount | BigDecimal | 欠款金额 |

#### ******* 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 门诊费用汇总数据 |
| 4   | success | Boolean | 是否成功 |

**门诊费用汇总数据结构（data）**：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| summaryReport | List | 门诊费用汇总报表 |
| totalIncome | BigDecimal | 门诊收入总计 |
| startDate | String | 开始日期 |
| endDate | String | 结束日期 |
| hospitalId | String | 医院ID |

**汇总报表数据结构（summaryReport）**：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| itemName | String | 项目名称（收费员姓名或支付类型） |
| amount | BigDecimal | 金额  |
| category | String | 分类（CASHIER/MEDICAL_INSURANCE/PAYMENT_CHANNEL/OTHER） |

#### 7.4.1.5 数据组装逻辑

**中间层系统组装逻辑**：

1.  **收费员金额汇总**：
    - 从收费员报表中提取每个收费员的现金金额
    - 按收费员姓名分组汇总
2.  **医保支付汇总**：
    - 从医保明细表中汇总各种支付类型
    - 包括：统筹支付、个人账户支付、补充医疗、公务员补助、大病保险、工伤保险、其他保险、院内优惠、医疗垫付、医保差额
3.  **支付渠道汇总**：
    - 从费用明细表中提取各种支付渠道金额
    - 包括：现金个人、支票个人、POS个人、公众号、支付中心、支付宝、健康德阳、诊间支付、银医通支付、优惠金额、欠款金额
4.  **门诊收入计算**：
    - 汇总所有收费员现金金额 + 所有支付渠道金额
    - 验证与费用明细表总金额一致性

#### 7.4.1.6 调用示例

**请求示例**：

{
"hospitalId": "001",
"startDate": "2025-07-13 00:00:00",
"endDate": "2025-07-13 23:59:59",
"medicalInsuranceDetails": \[
{
"settlementType": "城乡门诊慢特病清算",
"totalFee": 15286.16,
"poolPayment": 12827.65,
"personalAccount": 0,
"supplementaryMedical": 195.07,
"civilServantSubsidy": 0,
"seriousIllnessInsurance": 438.89,
"workInjuryInsurance": 0,
"otherInsurance": 1824.55,
"hospitalDiscount": 0,
"actualPayment": 5005.44,
"medicalAdvance": 0,
"medicalDifference": 0
}
\],
"cashierReports": \[
{
"operatorName": "刘涛",
"cashAmount": 7432.08,
"billNumber": "111321",
"refundCount": 0,
"refundAmount": 0,
"operationCount": 168,
"paymentCenterAmount": 6417.72,
"accountAmount": 9853.91,
"cardDeductionAmount": 0
}
\],
"feeDetails": {
"totalAmount": 309417.82,
"cashPersonal": 49762.48,
"checkPersonal": 0,
"posPersonal": 0,
"wechatPublic": 7923.56,
"paymentCenter": 51979.59,
"alipay": 7241.15,
"healthDeyang": 0,
"clinicPayment": 109849.36,
"bankMedicalPayment": 0,
"discountAmount": 0,
"arrearsAmount": 0
}
}

**响应示例**：

{
"code": 200,
"message": "门诊费用汇总成功",
"data": {
"summaryReport": \[
{
"itemName": "刘涛",
"amount": 7432.08,
"category": "CASHIER"
},
{
"itemName": "统筹支付",
"amount": 47122.63,
"category": "MEDICAL_INSURANCE"
},
{
"itemName": "公众号",
"amount": 7923.56,
"category": "PAYMENT_CHANNEL"
},
{
"itemName": "门诊收入",
"amount": 309417.82,
"category": "TOTAL"
}
\],
"totalIncome": 309417.82,
"startDate": "2025-07-13 00:00:00",
"endDate": "2025-07-13 23:59:59",
"hospitalId": "001"
},
"success": true
}

### 7.4.2 【/】住院费用明细接口

#### ******* 接口说明

我们系统调用对方公司此接口获取住院费用原始数据，包括医保明细、收费员报表、费用明细、支付方式四个报表数据。我们系统根据时间范围请求数据，对方返回原始数据后，我们负责组装成住院费用汇总报表。

#### ******* 接口路径

**请求方式**：POST

**接口路径**：由对方公司提供（待确定）

#### ******* 输入参数（我们发送给对方）

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | startDate | String | Y   | 开始日期，格式：yyyy-MM-dd HH:mm:ss |
| 3   | endDate | String | Y   | 结束日期，格式：yyyy-MM-dd HH:mm:ss |

#### 7.4.2.4 输出参数（对方返回给我们）

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 住院费用原始数据 |
| 4   | success | Boolean | 是否成功 |

**data 住院费用原始数据结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| medicalInsuranceDetails | List | 医保明细表数据 |
| cashierReports | List | 收费员报表数据 |
| feeDetails | Object | 费用明细数据 |
| paymentMethods | Object | 支付方式数据 |

**medicalInsuranceDetails 医保明细表数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
| --- | --- | --- | --- | --- | --- |
| 1   | settlementStartTime | String | Y   | 清算开始时间，格式：yyyy-MM-dd | "2025-07-13" |
| 2   | settlementEndTime | String | Y   | 清算结束时间，格式：yyyy-MM-dd | "2025-07-14" |
| 3   | settlementCenterCode | String | Y   | 清算分中心代码 | "1" |
| 4   | settlementCenterName | String | Y   | 清算分中心名称 | "按项目" |
| 5   | settlementCategoryCode | String | Y   | 清算类别代码 | "21" |
| 6   | settlementCategoryName | String | Y   | 清算类别名称 | "住院"、"生育住院"、"跨省异地住院" |
| 7   | totalAmount | BigDecimal | Y   | 费用总额，保留2位小数 | 23254.99 |
| 8   | totalPersons | Integer | Y   | 总人次 | 4   |
| 9   | personalAccountPayment | BigDecimal | N   | 个人账户支付金额，保留2位小数 | 106.00 |
| 10  | basicMedicalPayment | BigDecimal | N   | 基本医疗统筹支付金额，保留2位小数 | 12691.38 |
| 11  | civilServantSubsidy | BigDecimal | N   | 公务员补助报销金额，保留2位小数 | 2733.10 |
| 12  | supplementaryMedical | BigDecimal | N   | 补充医疗报销金额，保留2位小数 | 24.93 |
| 13  | majorIllnessInsurance | BigDecimal | N   | 大病保险赔付金额，保留2位小数 | 509.87 |
| 14  | employeeMajorIllness | BigDecimal | N   | 职工大病保险赔付金额，保留2位小数 | 0.00 |
| 15  | workInjuryInsurance | BigDecimal | N   | 工伤保险报销金额，保留2位小数 | 0.00 |
| 16  | hospitalBurden | BigDecimal | N   | 医院负担金额，保留2位小数 | 1088.20 |
| 17  | maternityInsurance | BigDecimal | N   | 生育保险赔付金额，保留2位小数 | 0.00 |
| 18  | otherInsurance | BigDecimal | N   | 其他保险金额，保留2位小数 | 1390.82 |
| 19  | medicalInsuranceDifference | BigDecimal | N   | 医保差额，保留2位小数 | 0.00 |
| 20  | medicalInsuranceExcess | BigDecimal | N   | 医保统筹超出部分，保留2位小数 | 0.00 |
| 21  | nationalMedicalInsurance | BigDecimal | N   | 国家医保，保留2位小数 | 0.00 |

**说明**：

- 必须包含汇总行数据，用于生成最终的医保支付统计
- 各种支付金额字段如果没有数据则传0.00，不能传null
- 清算类别包括：住院、生育住院、跨省异地住院、城乡居民生育清算、职工DRGS医疗费用清算、居民DRGS医疗费用清算

**cashierReports 收费员报表数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
| --- | --- | --- | --- | --- | --- |
| 1   | operatorName | String | Y   | 操作员姓名（收费员姓名） | "刘涛"、"易美琪"、"吕迎春" |
| 2   | billNumber | String | Y   | 账单序号 | "29637" |
| 3   | actualCash | BigDecimal | Y   | 实缴现金，保留2位小数，用于汇总收费员金额 | 800.00 |
| 4   | settlementSupplement | BigDecimal | N   | 结算结补，保留2位小数 | 0.00 |
| 5   | settlementRefund | BigDecimal | N   | 结算结退，保留2位小数 | 0.00 |
| 6   | startTime | String | Y   | 开始时间，格式：yyyy.MM.dd HH:mm:ss | "2025.07.12 23:55:03" |
| 7   | endTime | String | Y   | 结账时间，格式：yyyy.MM.dd HH:mm:ss | "2025.07.13 10:34:41" |
| 8   | receiptCount | Integer | N   | 收据张数 | 2   |
| 9   | invoiceCount | Integer | N   | 发票张数 | 0   |
| 10  | totalInvoiceAmount | BigDecimal | N   | 发票总费用，保留2位小数 | 0.00 |
| 11  | paymentStatus | String | N   | 缴款状态 | "未缴款"、"已缴款" |
| 12  | borrowCash | BigDecimal | N   | 借现金，保留2位小数 | 0.00 |
| 13  | lendCash | BigDecimal | N   | 贷现金，保留2位小数 | 0.00 |
| 14  | borrowCheck | BigDecimal | N   | 借支票，保留2位小数 | 0.00 |
| 15  | lendCheck | BigDecimal | N   | 贷支票，保留2位小数 | 0.00 |
| 16  | refundInvoiceAmount | BigDecimal | N   | 退票总费用，保留2位小数 | 0.00 |

**说明**：

- actualCash字段是关键字段，用于汇总各收费员的现金收入
- 必须包含指定时间范围内所有收费员的记录
- 金额字段如果没有数据则传0.00，不能传null

**feeDetails 费用明细数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
| --- | --- | --- | --- | --- | --- |
| 1   | statisticsPeriod | String | Y   | 统计期描述 | "统计期:2025年07月13日~~2025年07月14日" |
| 2   | midwaySettlementCount | Integer | Y   | 中途结算人次 | 6   |
| 3   | totalSettlementCount | Integer | Y   | 总共结算人次 | 83  |
| 4   | totalAmount | BigDecimal | Y   | 总费用（G+B+H+S+Q），保留2位小数，用于数据校验 | 538200.12 |
| 5   | arrearsSettlement | BigDecimal | N   | 欠费结算，保留2位小数 | 0.00 |
| 6   | personalPayment | BigDecimal | Y   | 个人支付（G），保留2位小数 | 272617.10 |
| 7   | medicalInsurancePayment | BigDecimal | Y   | 医保支付（B），保留2位小数 | 265583.02 |
| 8   | hospitalDiscount | BigDecimal | N   | 医院优惠（H），保留2位小数 | 0.00 |
| 9   | socialSecurityPayment | BigDecimal | N   | 社保支付（S），保留2位小数 | 0.00 |
| 10  | otherPayment | BigDecimal | N   | 其他支付（Q），保留2位小数 | 0.00 |
| 11  | rounding | BigDecimal | N   | 舍入，保留2位小数 | 0.00 |

**说明**：

- totalAmount用于与其他报表数据进行一致性校验
- personalPayment + medicalInsurancePayment + hospitalDiscount + socialSecurityPayment + otherPayment = totalAmount
- 统计期必须与请求的时间范围一致

**paymentMethods 支付方式数据结构**：

| **序号** | **参数名称** | **参数类型** | **是否必传** | **说明** | **示例值** |
| --- | --- | --- | --- | --- | --- |
| 1   | advancePaymentCollection | BigDecimal | Y   | 收取预交金（M），保留2位小数 | 338041.80 |
| 2   | advancePaymentCash | BigDecimal | Y   | 预交金现金（MX），保留2位小数 | 55491.00 |
| 3   | advancePaymentCheck | BigDecimal | Y   | 预交金支票（MZ），保留2位小数 | 282550.80 |
| 4   | qrCodePayment | BigDecimal | N   | 扫码付，保留2位小数 | 272150.80 |
| 5   | wechatPayment | BigDecimal | N   | 微信支付，保留2位小数 | 10400.00 |
| 6   | alipayPayment | BigDecimal | N   | 支付宝支付，保留2位小数 | 0.00 |
| 7   | selfServiceMachine | BigDecimal | N   | 自助机支付，保留2位小数 | 32500.00 |
| 8   | settlementRefund | BigDecimal | N   | 结算收退款（T），保留2位小数 | 2281.53 |
| 9   | bankDeposit | BigDecimal | N   | 银行缴款，保留2位小数 | 340323.33 |
| 10  | settlementAdvanceOffset | BigDecimal | Y   | 结算冲低预交款合计，保留2位小数 | 270335.57 |
| 11  | transferPayment | BigDecimal | N   | 转账支付，保留2位小数 | 0.00 |
| 12  | checkPayment | BigDecimal | N   | 支票支付，保留2位小数 | 0.00 |
| 13  | otherPayment | BigDecimal | N   | 其他支付方式，保留2位小数 | 0.00 |

**说明**：

- 支付方式数据用于生成支付渠道统计
- 各支付方式金额如果没有数据则传0.00，不能传null
- advancePaymentCash + advancePaymentCheck = advancePaymentCollection（数据一致性校验）
- 支付方式明细包括：扫码付、微信、支付宝、自助机、转账等具体渠道

#### 7.4.2.5 数据组装逻辑（我们系统内部处理）

**接收到对方数据后，我们系统的组装逻辑**：

1.  **收费员金额汇总**：
    - 从收费员报表中提取每个收费员的实缴现金金额
    - 按收费员姓名分组汇总，生成收费员金额明细
2.  **医保支付汇总**：
    - 从医保明细表中汇总各种医保支付类型
    - 包括：个人账户支付、基本医疗统筹支付、公务员补助、补充医疗、大病保险、其他保险、医院负担等
3.  **支付方式汇总**：
    - 从支付方式表中提取各种支付渠道金额
    - 包括：预交金现金、预交金支票、扫码付、微信支付、自助机支付、结算收退款等
4.  **住院收入计算**：
    - 汇总所有收费员现金金额 + 所有支付渠道金额
    - 验证与费用明细表总金额一致性
    - 生成最终的住院费用汇总报表（"导"表格式）

**对方公司需要提供的数据要求**：

- 必须包含完整的4个报表数据
- 数据时间范围必须与请求的startDate/endDate一致
- 金额字段保留2位小数精度
- 收费员报表必须包含实缴现金字段
- 医保明细表必须包含各种支付类型的汇总数据

#### 7.4.2.6 调用示例

**请求示例（我们发送给对方）**：

{
"hospitalId": "001",
"startDate": "2025-07-13 00:00:00",
"endDate": "2025-07-14 23:59:59"
}

**响应示例（对方返回给我们）**：

{
"code": 200,
"message": "查询成功",
"data": {
"medicalInsuranceDetails": \[
{
"settlementStartTime": "2025-07-13",
"settlementEndTime": "2025-07-14",
"settlementCenterCode": "1",
"settlementCenterName": "按项目",
"settlementCategoryCode": "21",
"settlementCategoryName": "住院",
"totalAmount": 23254.99,
"totalPersons": 4,
"personalAccountPayment": 106.00,
"basicMedicalPayment": 12691.38,
"supplementaryMedical": 24.93,
"majorIllnessInsurance": 509.87
},
{
"settlementCategoryCode": "99992",
"settlementCategoryName": "职工DRGS医疗费用清算",
"totalAmount": 37462.47,
"totalPersons": 8,
"personalAccountPayment": 2420.60,
"basicMedicalPayment": 23649.26,
"civilServantSubsidy": 2733.10,
"otherInsurance": 1390.82
}
\],
"cashierReports": \[
{
"operatorName": "刘涛",
"billNumber": "29637",
"actualCash": 800.00,
"startTime": "2025.07.12 23:55:03",
"endTime": "2025.07.13 10:34:41",
"receiptCount": 2,
"paymentStatus": "未缴款"
},
{
"operatorName": "易美琪",
"billNumber": "29640",
"actualCash": 12200.00,
"startTime": "2025.07.10 07:30:16",
"endTime": "2025.07.13 18:05:49",
"receiptCount": 16,
"paymentStatus": "未缴款"
}
\],
"feeDetails": {
"statisticsPeriod": "统计期:2025年07月13日~~2025年07月14日",
"midwaySettlementCount": 6,
"totalSettlementCount": 83,
"totalAmount": 538200.12,
"personalPayment": 272617.10,
"medicalInsurancePayment": 265583.02
},
"paymentMethods": {
"advancePaymentCollection": 338041.80,
"advancePaymentCash": 55491.00,
"advancePaymentCheck": 282550.80,
"qrCodePayment": 272150.80,
"wechatPayment": 10400.00,
"settlementRefund": 2281.53,
"bankDeposit": 340323.33,
"settlementAdvanceOffset": 270335.57
},
"success": true
}

### 7.4.3 【/】同步药品入库单接口

#### ******* 接口说明

ECS系统调用此接口同步卫林HIS系统的药品入库单视图数据，支持批量处理和分页查询。

#### ******* 接口路径

**请求方式**：POST

**接口路径**：/mid/sync/drugSync

#### ******* 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | id  | Integer | N   | 记录ID |
| 2   | stoinNum | String | N   | 入库单号 |
| 3   | stoinDate | String | N   | 入库日期 |
| 4   | totlcnt | Integer | N   | 数量合计 |
| 5   | rtalAmt | BigDecimal | N   | 零售金额 |
| 6   | purcpricAmt | BigDecimal | N   | 进价金额 |
| 7   | spler | String | N   | 供货单位 |
| 8   | syncDate | String | N   | 同步日期 |
| 9   | hospitalId | String | Y   | 医疗机构ID |
| 10  | xh  | Integer | N   | 序号  |
| 11  | floor | Integer | N   | 起始号 |
| 12  | offset | Integer | N   | 偏移量 |
| 13  | batchSize | Integer | N   | 每次批处理大小 |

#### 7.4.2.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.2.5 调用示例

**请求示例**：

{
"hospitalId": "H001",
"stoinDate": "2025-01-30",
"batchSize": 100,
"offset": 0
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 50,
"totalCount": 150
},
"success": true
}

### 7.4.3 【/】同步卫材入库单接口

#### 7.4.3.1 接口说明

用于同步卫材入库单视图数据，ECS系统通过此接口从SPD供应链系统获取卫材入库数据。

#### 7.4.3.2 接口路径

**请求方式**：POST

**接口路径**：/ecs/ecsSatmat/sanMatSync

#### 7.4.3.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | floor | Integer | N   | 起始序号 |
| 3   | offset | Integer | N   | 偏移量 |
| 4   | batchSize | Integer | N   | 批处理大小 |

#### 7.4.3.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 卫材入库单数据列表 |
| 4   | success | Boolean | 是否成功 |

### 7.4.4 【/】同步经济功能科目接口

#### 7.4.4.1 接口说明

ECS系统调用此接口同步用友财务系统的经济功能科目配置信息，确保报销科目的准确性。

#### 7.4.4.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/econFunSync

#### 7.4.4.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | subCode | String | N   | 科目代码 |
| 2   | subName | String | N   | 科目名称 |
| 3   | subType | String | N   | 科目类型 |
| 4   | pinyin | String | N   | 拼音助记码 |
| 5   | remarks | String | N   | 备注  |
| 6   | year | String | N   | 年度  |
| 7   | start | Integer | N   | 起始位置 |
| 8   | end | Integer | N   | 结束位置 |

#### 7.4.4.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.4.5 调用示例

**请求示例**：

{
"year": "2025",
"start": 0,
"end": 100
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 25,
"totalCount": 100
},
"success": true
}

### 7.4.5 【/】同步往来单位接口

#### 7.4.5.1 接口说明

ECS系统调用此接口同步用友财务系统的往来单位配置信息，包括单位基本信息、联系方式等。

#### 7.4.5.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/corrsInsSync

#### 7.4.5.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | id  | Integer | N   | 记录ID |
| 2   | insCode | String | N   | 单位代码 |
| 3   | insName | String | N   | 单位名称 |
| 4   | fulname | String | N   | 单位全称 |
| 5   | insType | String | N   | 单位类型 |
| 6   | commAddr | String | N   | 通信地址 |
| 7   | poscode | String | N   | 邮政编码 |
| 8   | conerName | String | N   | 联系人姓名 |
| 9   | conerTel | String | N   | 联系人电话 |
| 10  | fax | String | N   | 传真  |
| 11  | email | String | N   | 邮箱  |
| 12  | insNatu | String | N   | 单位属性 |
| 13  | hospitalId | String | Y   | 医疗机构ID |
| 14  | activeFlag | String | N   | 启用标志 |
| 15  | year | String | N   | 年度  |
| 16  | start | Integer | N   | 起始位置 |
| 17  | end | Integer | N   | 结束位置 |

#### 7.4.5.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.5.5 调用示例

**请求示例**：

{
"hospitalId": "H001",
"year": "2025",
"activeFlag": "1",
"start": 0,
"end": 50
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 15,
"totalCount": 50
},
"success": true
}

### 7.4.6 【/】同步项目信息接口

#### 7.4.6.1 接口说明

ECS系统调用此接口同步用友财务系统的项目配置信息，包括项目代码、名称、负责部门等。

#### 7.4.6.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/itemSync

#### 7.4.6.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | id  | Integer | N   | 记录ID |
| 2   | itemCode | String | N   | 项目代码 |
| 3   | itemName | String | N   | 项目名称 |
| 4   | pinyin | String | N   | 拼音助记码 |
| 5   | begnDate | String | N   | 开始日期 |
| 6   | endDate | String | N   | 结束日期 |
| 7   | dept | String | N   | 负责部门 |
| 8   | resper | String | N   | 负责人 |
| 9   | estaYear | String | N   | 立项年度 |
| 10  | hospitalId | String | Y   | 医疗机构ID |
| 11  | activeFlag | String | N   | 启用标志 |
| 12  | year | String | N   | 年度  |
| 13  | start | Integer | N   | 起始位置 |
| 14  | end | Integer | N   | 结束位置 |
| 15  | parentItemCode | String | N   | 父编码 |

#### 7.4.6.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.6.5 调用示例

**请求示例**：

{
"hospitalId": "H001",
"year": "2025",
"activeFlag": "1",
"start": 0,
"end": 100
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 30,
"totalCount": 100
},
"success": true
}

### 7.4.7 【/】同步现金流量接口

#### 7.4.7.1 接口说明

ECS系统调用此接口同步用友财务系统的现金流量配置信息，包括科目代码、流向性质等。

#### 7.4.7.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/asstCashSync

#### 7.4.7.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | id  | Integer | N   | 记录ID |
| 2   | subCode | String | N   | 科目代码 |
| 3   | subName | String | N   | 科目名称 |
| 4   | flowDire | String | N   | 流向性质 |
| 5   | pinyin | String | N   | 拼音助记码 |
| 6   | remarks | String | N   | 备注  |
| 7   | hospitalId | String | Y   | 医疗机构ID |
| 8   | activeFlag | String | N   | 启用标志 |
| 9   | year | String | N   | 年度  |
| 10  | start | Integer | N   | 起始位置 |
| 11  | end | Integer | N   | 结束位置 |
| 12  | parentSubCode | String | N   | 父编码 |

#### 7.4.7.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.7.5 调用示例

**请求示例**：

{
"hospitalId": "H001",
"year": "2025",
"start": 0,
"end": 50
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 20,
"totalCount": 50
},
"success": true
}

### 7.4.8 【/】同步会计科目接口

#### 7.4.8.1 接口说明

ECS系统调用此接口同步用友财务系统的会计科目配置信息，包括科目代码、类型、会计要素等。

#### 7.4.8.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/actigSync

#### 7.4.8.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | id  | Integer | N   | 记录ID |
| 2   | subCode | String | N   | 科目代码 |
| 3   | subName | String | N   | 科目名称 |
| 4   | subType | String | N   | 科目类型 |
| 5   | remarks | String | N   | 备注  |
| 6   | pinyin | String | N   | 拼音助记码 |
| 7   | asstInfo | String | N   | 辅助信息 |
| 8   | actigElem | String | N   | 会计要素 |
| 9   | actigSys | String | N   | 会计体系 |
| 10  | balcDirc | String | N   | 余额方向 |
| 11  | empType | String | N   | 单位类型 |
| 12  | hospitalId | String | Y   | 医疗机构ID |
| 13  | activeFlag | String | N   | 启用标志 |
| 14  | year | String | N   | 年度  |
| 15  | start | Integer | N   | 起始位置 |
| 16  | end | Integer | N   | 结束位置 |
| 17  | parentCode | String | N   | 父科目代码 |
| 18  | empTypeArr | String\[\] | N   | 单位类型数组 |
| 19  | asstInfoArr | String\[\] | N   | 辅助信息数组 |
| 20  | status | String | N   | 状态  |

#### 7.4.8.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果数据 |
| 4   | success | Boolean | 是否成功 |

#### 7.4.8.5 调用示例

**请求示例**：

{
"hospitalId": "H001",
"year": "2025",
"activeFlag": "1",
"start": 0,
"end": 100
}

**响应示例**：

{
"code": 200,
"message": "同步成功",
"data": {
"syncCount": 45,
"totalCount": 100
},
"success": true
}



## 第8章 ERP财务核算系统接口

### 8.1 接口地址

#### 8.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/erp
- **内网地址**：https://10.2.233.11:8090/back/erp

#### 8.1.2 具体接口地址
- **工资数据查询**：https://hrp.zjxrmyy.cn:18090/back/hrm/employeeSalary/mySalary（通过HRM系统）
- **用友数据同步**：http://172.16.252.24:9527/mid/yy/*（通过中间层）

### 8.2 业务流程图

```mermaid
graph TD
    A[外部系统] --> B[ERP财务核算系统]
    B --> C[凭证管理]
    B --> D[工资数据查询]
    B --> E[用友系统同步]

    C --> C1[凭证信息查询]
    C --> C2[凭证生成]
    C --> C3[凭证删除]

    D --> D1[员工工资查询]
    D --> D2[工资统计分析]

    E --> E1[预算执行查询]
    E --> E2[科目同步]
    E --> E3[往来单位同步]

    C1 --> F[中间层]
    C2 --> F
    C3 --> F

    F --> G[用友财务系统]
    E1 --> G
    E2 --> G
    E3 --> G

    style A fill:#e1f5fe
    style B fill:#f1f8e9
    style F fill:#fff8e1
    style G fill:#fff3e0
```

### 8.3 接口内容

#### 8.2.1 数据接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** | **状态** |
| --- | --- | --- | --- | --- | --- | --- |
| /   | 工资数据查询 | HTTP POST | 通过HRM系统查询员工工资数据 | HRM系统 | 已实现 |

#### 8.2.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** | **状态** |
| --- | --- | --- | --- | --- | --- | --- |
| /   | 用友数据同步 | HTTP POST | 通过中间层同步用友系统基础数据 | 中间层系统 | 已实现 |

## 第8.2 接口地址与调用

### 8.2.1 基础配置

**ERP系统服务器地址**：http://172.16.252.24:9527

**接口前缀**：/erp

**中间层服务器地址**：http://172.16.252.24:9527

**中间层接口前缀**：/mid

### 8.3 接口URL映射

根据系统配置，各接口的完整URL如下：

| **功能模块** | **接口路径** | **完整URL** | **调用方** | **状态** |
| --- | --- | --- | --- | --- |
| 工资数据查询 | /employeeSalary/mySalary | http://172.16.252.24:9527/hrm/employeeSalary/mySalary | HRM系统 | 已实现 |
| 用友数据同步 | /yy/* | http://172.16.252.24:9527/mid/yy/* | 中间层系统 | 已实现 |

## 第8.3 通用响应格式

### 8.3.1 成功响应

{
"code": 200,
"message": "操作成功",
"data": {
// 具体业务数据
},
"success": true
}

### 8.3.2 分页响应

{
"code": 200,
"message": "查询成功",
"data": {
"records": \[
// 数据列表
\],
"total": 100,
"size": 10,
"current": 1,
"pages": 10
},
"success": true
}

### 8.3.3 错误响应

{
"code": 500,
"message": "操作失败",
"data": null,
"success": false
}



## 第8.4 ERP系统接口详细说明

### 8.4.1 【/】凭证信息查询接口

#### 8.4.1.1 接口说明

ERP系统内部调用此接口查询财务凭证信息列表，支持分页查询和条件过滤。该接口用于凭证管理模块的数据展示。

#### 8.4.1.2 接口路径

**请求方式**：POST

**接口路径**：/erp/erpVcrDetail/list

#### 8.4.1.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | pageNum | Integer | N   | 页码，默认1 |
| 3   | pageSize | Integer | N   | 每页大小，默认10 |
| 4   | vcrNo | String | N   | 凭证号 |
| 5   | vcrDate | String | N   | 凭证日期 |
| 6   | vcrType | String | N   | 凭证类型 |

#### 8.4.1.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 分页数据 |
| 4   | success | Boolean | 是否成功 |

**data 分页数据结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| records | List | 凭证信息列表 |
| total | Long | 总记录数 |
| size | Long | 每页大小 |
| current | Long | 当前页码 |
| pages | Long | 总页数 |

**records 凭证信息结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| id  | Integer | 凭证ID |
| vcrNo | String | 凭证号 |
| vcrDate | String | 凭证日期 |
| vcrType | String | 凭证类型 |
| totalAmount | BigDecimal | 凭证金额 |
| summary | String | 摘要  |
| createTime | String | 创建时间 |
| creator | String | 创建人 |

#### 8.4.1.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"pageNum": 1,
"pageSize": 10,
"vcrType": "报销凭证"
}

**响应示例**：

{
"code": 200,
"message": "查询成功",
"data": {
"records": \[
{
"id": 1,
"vcrNo": "PZ202501001",
"vcrDate": "2025-01-15",
"vcrType": "报销凭证",
"totalAmount": 5000.00,
"summary": "差旅费报销",
"createTime": "2025-01-15 10:30:00",
"creator": "张三"
}
\],
"total": 1,
"size": 10,
"current": 1,
"pages": 1
},
"success": true
}

### 8.4.2 【/】凭证删除接口

#### 8.4.2.1 接口说明

ERP系统内部调用此接口删除财务凭证信息。删除操作会同时调用中间层接口删除外部财务系统的凭证数据。

#### 8.4.2.2 接口路径

**请求方式**：DELETE

**接口路径**：/erp/erpVcrDetail/delete

#### 8.4.2.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | ids | List | Y   | 凭证ID列表 |
| 3   | vcrNos | List | N   | 凭证号列表 |

#### 8.4.2.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 删除结果 |
| 4   | success | Boolean | 是否成功 |

#### 8.4.2.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"ids": \[1, 2, 3\],
"vcrNos": \["PZ202501001", "PZ202501002", "PZ202501003"\]
}

**响应示例**：

{
"code": 200,
"message": "删除成功",
"data": null,
"success": true
}

### 8.4.3 【/】待生成凭证查询接口

#### 8.4.3.1 接口说明

ERP系统内部调用此接口查询待生成凭证的报销项信息，用于凭证生成功能的数据源。

#### 8.4.3.2 接口路径

**请求方式**：POST

**接口路径**：/erp/erpVcrDetail/vcrGenList

#### 8.4.3.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | pageNum | Integer | N   | 页码，默认1 |
| 3   | pageSize | Integer | N   | 每页大小，默认10 |
| 4   | startDate | String | N   | 开始日期 |
| 5   | endDate | String | N   | 结束日期 |
| 6   | reimType | String | N   | 报销类型 |
| 7   | status | String | N   | 状态  |

#### 8.4.3.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 分页数据 |
| 4   | success | Boolean | 是否成功 |

**data 分页数据结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| records | List | 待生成凭证列表 |
| total | Long | 总记录数 |
| size | Long | 每页大小 |
| current | Long | 当前页码 |
| pages | Long | 总页数 |

**records 待生成凭证结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| reimId | Integer | 报销ID |
| reimNo | String | 报销单号 |
| reimType | String | 报销类型 |
| reimAmount | BigDecimal | 报销金额 |
| reimPerson | String | 报销人 |
| reimDept | String | 报销部门 |
| reimDate | String | 报销日期 |
| status | String | 状态  |

#### 8.4.3.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"pageNum": 1,
"pageSize": 10,
"startDate": "2025-01-01",
"endDate": "2025-01-31",
"reimType": "差旅费"
}

**响应示例**：

{
"code": 200,
"message": "查询成功",
"data": {
"records": \[
{
"reimId": 1001,
"reimNo": "BX202501001",
"reimType": "差旅费",
"reimAmount": 2500.00,
"reimPerson": "张三",
"reimDept": "财务部",
"reimDate": "2025-01-15",
"status": "待生成凭证"
}
\],
"total": 1,
"size": 10,
"current": 1,
"pages": 1
},
"success": true
}

### 8.4.4 【/】药品凭证查询接口

#### 8.4.4.1 接口说明

ERP系统内部调用此接口查询药品相关的待生成凭证信息，专门用于药品采购和入库的凭证生成。

#### 8.4.4.2 接口路径

**请求方式**：POST

**接口路径**：/erp/erpVcrDetail/drugToVcrList

#### 8.4.4.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | pageNum | Integer | N   | 页码，默认1 |
| 3   | pageSize | Integer | N   | 每页大小，默认10 |
| 4   | startDate | String | N   | 开始日期 |
| 5   | endDate | String | N   | 结束日期 |
| 6   | drugType | String | N   | 药品类型 |

#### 8.4.4.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 分页数据 |
| 4   | success | Boolean | 是否成功 |

### 8.4.5 【/】工资凭证查询接口

#### 8.4.5.1 接口说明

ERP系统内部调用此接口查询工资相关的待生成凭证信息，用于工资发放的凭证生成。

#### 8.4.5.2 接口路径

**请求方式**：POST

**接口路径**：/erp/erpVcrDetail/salaryToVcrList

#### 8.4.5.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | pageNum | Integer | N   | 页码，默认1 |
| 3   | pageSize | Integer | N   | 每页大小，默认10 |
| 4   | salaryMonth | String | N   | 工资月份 |
| 5   | deptId | String | N   | 部门ID |

### 8.4.6 【/】凭证生成接口

#### 8.4.6.1 接口说明

ERP系统内部调用此接口生成财务凭证，会同时调用中间层接口将凭证推送到外部财务系统。

#### 8.4.6.2 接口路径

**请求方式**：POST

**接口路径**：/erp/erpVcrDetail/generateVcr

#### 8.4.6.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | vcrType | String | Y   | 凭证类型 |
| 3   | vcrDate | String | Y   | 凭证日期 |
| 4   | summary | String | Y   | 摘要  |
| 5   | details | List | Y   | 凭证明细列表 |

**details 凭证明细结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| accountCode | String | Y   | 会计科目代码 |
| accountName | String | Y   | 会计科目名称 |
| debitAmount | BigDecimal | N   | 借方金额 |
| creditAmount | BigDecimal | N   | 贷方金额 |
| summary | String | Y   | 明细摘要 |
| assistItems | List | N   | 辅助项目列表 |

#### 8.4.6.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 生成结果 |
| 4   | success | Boolean | 是否成功 |

**data 生成结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| vcrId | Integer | 凭证ID |
| vcrNo | String | 凭证号 |
| externalVcrNo | String | 外部系统凭证号 |

#### 8.4.6.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"vcrType": "报销凭证",
"vcrDate": "2025-01-15",
"summary": "差旅费报销",
"details": \[
{
"accountCode": "6601",
"accountName": "差旅费",
"debitAmount": 2500.00,
"creditAmount": 0.00,
"summary": "张三差旅费报销"
},
{
"accountCode": "1002",
"accountName": "银行存款",
"debitAmount": 0.00,
"creditAmount": 2500.00,
"summary": "银行转账支付"
}
\]
}

**响应示例**：

{
"code": 200,
"message": "凭证生成成功",
"data": {
"vcrId": 1001,
"vcrNo": "PZ202501001",
"externalVcrNo": "YY202501001"
},
"success": true
}

### 8.4.7 【/】中间层凭证生成接口

#### 8.4.7.1 接口说明

ERP系统调用中间层此接口生成财务凭证并推送到外部财务系统（用友系统）。中间层负责数据转换和外部系统集成。

#### 8.4.7.2 接口路径

**请求方式**：POST

**接口路径**：/mid/vcr/generate

#### 8.4.7.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | vcrType | String | Y   | 凭证类型 |
| 3   | vcrDate | String | Y   | 凭证日期，格式：yyyy-MM-dd |
| 4   | summary | String | Y   | 凭证摘要 |
| 5   | totalAmount | BigDecimal | Y   | 凭证总金额 |
| 6   | details | List | Y   | 凭证明细列表 |
| 7   | creator | String | Y   | 创建人 |
| 8   | createTime | String | Y   | 创建时间 |

**details 凭证明细结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| accountCode | String | Y   | 会计科目代码 |
| accountName | String | Y   | 会计科目名称 |
| debitAmount | BigDecimal | N   | 借方金额，保留2位小数 |
| creditAmount | BigDecimal | N   | 贷方金额，保留2位小数 |
| summary | String | Y   | 明细摘要 |
| assistItems | List | N   | 辅助项目列表 |

#### 8.4.7.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 生成结果 |
| 4   | success | Boolean | 是否成功 |

**data 生成结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| externalVcrNo | String | 外部系统凭证号 |
| externalVcrId | String | 外部系统凭证ID |
| pushTime | String | 推送时间 |
| pushStatus | String | 推送状态 |

#### 8.4.7.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"vcrType": "报销凭证",
"vcrDate": "2025-01-15",
"summary": "差旅费报销",
"totalAmount": 2500.00,
"creator": "张三",
"createTime": "2025-01-15 10:30:00",
"details": \[
{
"accountCode": "6601",
"accountName": "差旅费",
"debitAmount": 2500.00,
"creditAmount": 0.00,
"summary": "张三差旅费报销"
},
{
"accountCode": "1002",
"accountName": "银行存款",
"debitAmount": 0.00,
"creditAmount": 2500.00,
"summary": "银行转账支付"
}
\]
}

**响应示例**：

{
"code": 200,
"message": "凭证生成成功",
"data": {
"externalVcrNo": "YY202501001",
"externalVcrId": "100001",
"pushTime": "2025-01-15 10:35:00",
"pushStatus": "成功"
},
"success": true
}

### 8.4.8 【/】中间层凭证删除接口

#### 8.4.8.1 接口说明

ERP系统调用中间层此接口删除外部财务系统的凭证数据。中间层负责调用外部系统API完成删除操作。

#### 8.4.8.2 接口路径

**请求方式**：POST

**接口路径**：/mid/vcr/delete

#### 8.4.8.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | vcrNos | List | Y   | 外部系统凭证号列表 |
| 3   | deleteReason | String | N   | 删除原因 |
| 4   | operator | String | Y   | 操作人 |

#### 8.4.8.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 删除结果 |
| 4   | success | Boolean | 是否成功 |

**data 删除结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| successCount | Integer | 成功删除数量 |
| failCount | Integer | 删除失败数量 |
| failDetails | List | 失败详情列表 |

#### 8.4.8.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"vcrNos": \["YY202501001", "YY202501002"\],
"deleteReason": "凭证错误需要重新生成",
"operator": "张三"
}

**响应示例**：

{
"code": 200,
"message": "删除完成",
"data": {
"successCount": 2,
"failCount": 0,
"failDetails": \[\]
},
"success": true
}

### 8.4.9 【/】工资数据查询接口

#### 8.4.9.1 接口说明

ERP系统对外提供此接口查询员工工资数据，从用友系统获取实发工资信息。支持按员工、部门、时间范围查询工资条信息。

#### 8.4.9.2 接口路径

**请求方式**：POST

**接口路径**：/erp/employeeSalary/mySalary

#### 8.4.9.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | empCode | String | N   | 员工编码 |
| 3   | deptId | String | N   | 部门ID |
| 4   | salaryMonth | String | N   | 工资月份，格式：yyyy-MM |
| 5   | startMonth | String | N   | 开始月份，格式：yyyy-MM |
| 6   | endMonth | String | N   | 结束月份，格式：yyyy-MM |

#### 8.4.9.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 工资数据列表 |
| 4   | success | Boolean | 是否成功 |

**data 工资数据结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| empCode | String | 员工编码 |
| empName | String | 员工姓名 |
| deptName | String | 部门名称 |
| salaryMonth | String | 工资月份 |
| baseSalary | BigDecimal | 基本工资 |
| allowance | BigDecimal | 津贴补贴 |
| bonus | BigDecimal | 奖金  |
| deduction | BigDecimal | 扣款  |
| realSalary | BigDecimal | 实发工资 |
| socialInsurance | BigDecimal | 社保扣款 |
| tax | BigDecimal | 个人所得税 |

#### 8.4.9.5 调用示例

**请求示例**：

{
"hospitalId": "001",
"empCode": "E001",
"salaryMonth": "2025-01"
}

**响应示例**：

{
"code": 200,
"message": "查询成功",
"data": \[
{
"empCode": "E001",
"empName": "张三",
"deptName": "财务部",
"salaryMonth": "2025-01",
"baseSalary": 8000.00,
"allowance": 1500.00,
"bonus": 2000.00,
"deduction": 200.00,
"realSalary": 9800.00,
"socialInsurance": 1200.00,
"tax": 300.00
}
\],
"success": true
}

### 8.4.10 【/】预算执行查询接口

#### 8.4.10.1 接口说明

ERP系统对外提供此接口查询用友系统预算执行情况，支持按部门、科目、时间范围查询预算执行和分析数据。

#### 8.4.10.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/queryBudgetExecution

#### 8.4.10.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | deptId | String | N   | 部门ID |
| 3   | accountCode | String | N   | 会计科目代码 |
| 4   | startDate | String | Y   | 开始日期，格式：yyyy-MM-dd |
| 5   | endDate | String | Y   | 结束日期，格式：yyyy-MM-dd |
| 6   | budgetType | String | N   | 预算类型 |

#### 8.4.10.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 预算执行数据列表 |
| 4   | success | Boolean | 是否成功 |

### 8.4.11 【/】经济功能科目同步接口

#### 8.4.11.1 接口说明

ERP系统调用此接口同步用友系统经济功能科目配置信息，确保财务科目体系的一致性。

#### 8.4.11.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/econFunSync

#### 8.4.11.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | syncType | String | N   | 同步类型：full-全量，increment-增量 |
| 3   | lastSyncTime | String | N   | 上次同步时间 |

#### 8.4.11.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 同步结果 |
| 4   | success | Boolean | 是否成功 |

### 8.4.12 【/】往来单位同步接口

#### 8.4.12.1 接口说明

ERP系统调用此接口同步用友系统供应商客户档案信息，保持往来单位数据的一致性。

#### 8.4.12.2 接口路径

**请求方式**：POST

**接口路径**：/mid/yy/corrslnsSync

#### 8.4.12.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID |
| 2   | unitType | String | N   | 单位类型：supplier-供应商，customer-客户 |
| 3   | syncType | String | N   | 同步类型：full-全量，increment-增量 |

## 第9章 MMIS物资管理系统接口

### 9.1 接口地址

#### 9.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/mmis/externalApi/gateway
- **内网地址**：https://10.2.233.11:8090/back/mmis/externalApi/gateway

#### 9.1.2 接口调用说明
- **统一网关**：所有MMIS接口通过统一网关调用
- **接口区分**：通过请求参数中的`infno`字段区分具体接口（M1001-M1010）
- **认证方式**：hospitalId + encryptKey统一认证
- **加密秘钥**：encryptKey需要向我们申请获取

### 9.2 接口内容和列表

### 9.2.1 数据接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** |
| --- | --- | --- | --- | --- |
| M1001 | 获取物资分类接口 | HTTP POST | 获取系统中所有物资分类信息 | 外部系统 |
| M1002 | 获取物资项目接口 | HTTP POST | 获取系统中所有物资项目信息 | 外部系统 |
| M1005 | 查询出库备份记录接口 | HTTP POST | 查询出库操作的备份记录 | 外部系统 |
| M1006 | 查询物资出库记录接口 | HTTP POST | 查询物资出库申请记录 | 外部系统 |
| M1008 | 查询预出库记录列表接口 | HTTP POST | 查询预出库记录列表（开发中） | 外部系统 |
| M1009 | 获取待确认出库工单列表接口 | HTTP POST | 获取需要确认的出库工单 | 外部系统 |
| M1010 | 获取可撤销工单列表接口 | HTTP POST | 获取可以撤销的出库工单 | 外部系统 |

### 9.2.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** |
| --- | --- | --- | --- | --- |
| M1003 | 确认预出库接口 | HTTP POST | 确认预出库单据，完成出库流程 | 外部系统 |
| M1004 | 物资出库撤销接口 | HTTP POST | 撤销已完成的出库单据 | 外部系统 |
| M1007 | 物资预出库接口 | HTTP POST | 创建预出库单据，进行库存检查 | 外部系统 |

## 9.2 接口地址与URL映射配置

### 9.2.1 统一网关接口

| **接口名称** | **接口路径** | **完整URL** | **调用方** |
| --- | --- | --- | --- |
| 统一网关入口 | /gateway | http://ip:port/externalApi/gateway | 外部系统 |

### 9.2.2 认证信息

- **医院ID：** zjxrmyy
- **加密密钥：** 57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E

## 9.3 通用响应格式规范

### 9.3.1 成功响应格式

{
"code": 200,
"message": "成功",
"data": {
// 具体业务数据
}
}

### 9.3.2 分页响应格式

{
"code": 200,
"message": "成功",
"data": {
"records": \[
// 数据列表
\],
"total": 100,
"size": 20,
"current": 1,
"pages": 5
}
}

### 9.3.3 错误响应格式

{
"code": 400,
"message": "请求参数错误",
"data": null
}

## 9.4 业务流程说明

### 9.4.1 服务接口调用顺序

MMIS系统提供两种主要的出库业务流程：**标准预出库流程** 和 **紧急直接出库流程**。

#### ******* MMIS系统出库业务流程

**流程说明**：

1.  **选择物资分类**：通过M1001接口获取物资分类信息，选择需要出库的物资类别
2.  **选择物资项目**：通过M1002接口获取具体的物资项目，确定出库的具体物资
3.  **预出库**：通过M1007接口进行预出库操作，系统进行库存检查和预留
4.  **库存检查**：系统自动检查库存是否充足
    - 如果库存充足：进入确认出库流程
    - 如果库存不足：需要撤销预出库操作
5.  **确认出库**：通过M1003接口确认预出库，完成实际的出库操作
6.  **撤销预出库**：如果库存不足或其他原因，通过M1004接口撤销预出库操作
7.  **出库完成/失败**：根据操作结果完成整个出库流程

**备注**：

- 所有出库记录可通过M1006接口查询
- 撤销操作的备份记录可通过M1005接口查询

### 9.4.2 业务流程说明

1.  **数据准备阶段**：
    - 调用 M1001 获取物资分类信息
    - 调用 M1002 获取物资项目详情和库存信息
2.  **标准出库流程**：
    - 调用 M1007 创建预出库单据，系统进行库存检查
    - 调用 M1009 获取待确认的工单列表
    - 调用 M1003 确认预出库，完成实际出库操作
3.  **查询和监控**：
    - 调用 M1006 查询出库记录
    - 调用 M1008 查询预出库记录（开发中）
4.  **异常处理**：
    - 调用 M1010 获取可撤销的工单
    - 调用 M1004 执行撤销操作
    - 调用 M1005 查询撤销备份记录

## 9.5 详细接口规范

### 9.5.1 【M1001】获取物资分类接口

#### 9.5.1.1 接口说明

获取MMIS系统中所有物资分类信息，支持树形结构的分类数据查询。

#### 9.5.1.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.1.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1001 |
| 4   | input | Object | N   | 输入参数，此接口为空对象 |

#### ******* 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 物资分类数据列表 |

**data 物资分类结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| tripartiteConsumableTypeId | String | 物资分类ID |
| code | String | 分类编码 |
| name | String | 分类名称 |
| parentId | String | 父分类编码 |
| isLeaf | Integer | 是否是叶子节点，0否 1是 |
| deleteFlag | Integer | 数据删除状态，0未删除 1已删除 |

#### ******* 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1001",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"tripartiteConsumableTypeId": "TYPE001",
"code": "HC001",
"name": "医用耗材",
"parentId": null,
"isLeaf": 0,
"deleteFlag": 0
},
{
"tripartiteConsumableTypeId": "TYPE002",
"code": "HC001001",
"name": "注射器类",
"parentId": "HC001",
"isLeaf": 1,
"deleteFlag": 0
}
\]
}

### 9.5.2 【M1002】获取物资项目接口

#### ******* 接口说明

获取MMIS系统中所有物资项目信息，包括物资的基本信息、库存数量、价格等详细数据。

#### ******* 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******* 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1002 |
| 4   | input | Object | N   | 输入参数，此接口为空对象 |

#### ******* 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 物资项目数据列表 |

**data 物资项目结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| tripartiteConsumableId | String | 物资ID |
| code | String | 物资编码 |
| name | String | 物资名称 |
| unit | String | 计量单位 |
| cost | BigDecimal | 单价  |
| specification | String | 规格  |
| isPutaway | Integer | 是否上架，0否 1是 |
| deleteFlag | Integer | 数据删除状态，0未删除 1已删除 |
| tripartiteConsumableTypeId | String | 分类ID |
| stockNum | BigDecimal | 库存数量 |

#### ******* 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1002",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"tripartiteConsumableId": "MAT001",
"code": "HC001001001",
"name": "一次性注射器5ml",
"unit": "支",
"cost": 1.50,
"specification": "5ml",
"isPutaway": 1,
"deleteFlag": 0,
"tripartiteConsumableTypeId": "TYPE002",
"stockNum": 1000.00
}
\]
}

### 9.5.3 【M1003】确认预出库接口

#### ******* 接口说明

确认预出库单据，完成实际的出库流程。此接口用于将预出库状态的单据转为正式出库。

#### ******* 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******* 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1003 |
| 4   | input | Object | Y   | 确认出库输入参数 |

**input 确认出库参数结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workCode | String | N   | 工单唯一编码（可选） |
| outboundOrderNumber | String | Y   | 出库单号 |

#### 9.5.3.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 确认结果数据 |

**data 确认结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| status | String | 处理状态 |
| message | String | 处理消息 |

#### 9.5.3.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1003",
"input": {
"workCode": "PWO20250131-001",
"outboundOrderNumber": "OUT20250131001"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"workId": "PWO20250131-001",
"status": "SUCCESS",
"message": "确认出库成功"
}
}

### 9.5.4 【M1004】物资出库撤销接口

#### 9.5.4.1 接口说明

撤销已完成的出库单据，将出库的物资重新入库，并记录撤销操作的备份信息。

#### 9.5.4.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.4.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1004 |
| 4   | input | Object | Y   | 撤销出库输入参数 |

**input 撤销出库参数结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workId | String | Y   | 工单ID |
| revokeReason | String | Y   | 撤销原因 |
| operator | String | Y   | 操作人 |

#### 9.5.4.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 撤销结果数据 |

**data 撤销结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| status | String | 处理状态 |
| message | String | 处理消息 |
| revokeTime | String | 撤销时间 |

#### 9.5.4.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1004",
"input": {
"workId": "PWO20250131-001",
"revokeReason": "申请部门取消需求",
"operator": "admin"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"workId": "PWO20250131-001",
"status": "SUCCESS",
"message": "撤销出库成功",
"revokeTime": "2025-01-31 15:30:00"
}
}

### 9.5.5 【M1005】查询出库备份记录接口

#### 9.5.5.1 接口说明

查询出库操作的备份记录，包括撤销操作的历史记录和详细信息。

#### 9.5.5.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.5.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1005 |
| 4   | input | Object | Y   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workId | String | N   | 工单ID（可选） |
| status | String | N   | 状态：ACTIVE(生效)、REVOKED(已撤销) |
| startDate | String | N   | 开始日期，格式：yyyy-MM-dd |
| endDate | String | N   | 结束日期，格式：yyyy-MM-dd |

#### 9.5.5.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 备份记录列表 |

**data 备份记录结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| workCode | String | 工单编码 |
| outboundId | String | 出库单ID |
| status | String | 状态  |
| consumableList | Array | 物资列表 |
| revokeTime | String | 撤销时间 |
| revokeReason | String | 撤销原因 |
| processResult | String | 处理结果 |

#### ******* 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1005",
"input": {
"status": "REVOKED",
"startDate": "2025-01-01",
"endDate": "2025-01-31"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"workId": "PWO20250131-001",
"workCode": "PWO20250131-001",
"outboundId": "OUT20250131001",
"status": "REVOKED",
"consumableList": \[
{
"tripartiteConsumableId": "MAT001",
"num": 10
}
\],
"revokeTime": "2025-01-31 15:30:00",
"revokeReason": "申请部门取消需求",
"processResult": "SUCCESS"
}
\]
}

### 9.5.6 【M1006】查询物资出库记录接口

#### ******* 接口说明

查询物资出库申请记录，包括出库单据的详细信息和状态。

#### ******* 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******* 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1006 |
| 4   | input | Object | Y   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workId | String | N   | 工单ID（可选） |
| orgId | String | N   | 申请科室ID |
| chkState | String | N   | 审核状态 |
| startDate | String | N   | 开始日期，格式：yyyy-MM-dd |
| endDate | String | N   | 结束日期，格式：yyyy-MM-dd |

#### 9.5.6.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 出库记录列表 |

**data 出库记录结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| id  | Integer | 记录ID |
| appyOrgId | String | 申请科室 |
| appyer | String | 申请人 |
| chkState | String | 审核状态 |
| billDate | String | 开单日期 |
| docmentNum | String | 单据号 |
| purpose | String | 用途  |

#### 9.5.6.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1006",
"input": {
"chkState": "APPROVED",
"startDate": "2025-01-01",
"endDate": "2025-01-31"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"id": 1001,
"appyOrgId": "DEPT001",
"appyer": "张医生",
"chkState": "APPROVED",
"billDate": "2025-01-31",
"docmentNum": "OUT20250131001",
"purpose": "手术用耗材"
}
\]
}

### 9.5.7 【M1007】物资预出库接口

#### 9.5.7.1 接口说明

创建预出库单据，进行库存检查和预留，为后续确认出库做准备。

#### 9.5.7.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.7.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1007 |
| 4   | input | Object | Y   | 预出库数据 |

**input 预出库数据结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workCode | String | Y   | 工单唯一编码 |
| business_dept | String | Y   | 业务部门唯一ID |
| use_dept | String | Y   | 送达科室唯一ID |
| applicant | String | Y   | 申请人(工单处理工程师工号) |
| createTime | String | Y   | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| consumableList | Array | Y   | 物资列表 |

**consumableList 物资项结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| tripartiteConsumableId | String | Y   | 出库耗材唯一ID |
| num | BigDecimal | Y   | 出库耗材数量 |

#### ******* 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 预出库结果 |

**data 预出库结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| status | String | 处理状态 |
| message | String | 处理消息 |
| outboundOrderNumber | String | 出库单号 |

#### ******* 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1007",
"input": {
"workCode": "PWO20250131-002",
"business_dept": "DEPT001",
"use_dept": "DEPT002",
"applicant": "EMP001",
"createTime": "2025-01-31 10:00:00",
"consumableList": \[
{
"tripartiteConsumableId": "MAT001",
"num": 10
},
{
"tripartiteConsumableId": "MAT002",
"num": 5
}
\]
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"workId": "PWO20250131-002",
"status": "SUCCESS",
"message": "预出库成功",
"outboundOrderNumber": "OUT20250131002"
}
}

### 9.5.8 【M1008】查询预出库记录列表接口

#### ******* 接口说明

查询预出库记录列表，获取预出库单据的状态和详细信息。（此接口正在开发中）

#### ******* 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******* 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1008 |
| 4   | input | Object | Y   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| workCode | String | N   | 工单编码（可选） |
| status | String | N   | 预出库状态 |
| startDate | String | N   | 开始日期，格式：yyyy-MM-dd |
| endDate | String | N   | 结束日期，格式：yyyy-MM-dd |

#### 9.5.8.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 预出库记录列表 |

**data 预出库记录结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| workCode | String | 工单编码 |
| status | String | 预出库状态 |
| createTime | String | 创建时间 |
| applicant | String | 申请人 |
| business_dept | String | 业务部门 |
| use_dept | String | 使用科室 |

#### 9.5.8.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1008",
"input": {
"status": "PENDING",
"startDate": "2025-01-01",
"endDate": "2025-01-31"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"workId": "PWO20250131-002",
"workCode": "PWO20250131-002",
"status": "PENDING",
"createTime": "2025-01-31 10:00:00",
"applicant": "EMP001",
"business_dept": "DEPT001",
"use_dept": "DEPT002"
}
\]
}

### 9.5.9 【M1009】获取待确认出库工单列表接口

#### 9.5.9.1 接口说明

获取需要确认的出库工单列表，用于查看哪些预出库单据可以进行确认操作。

#### 9.5.9.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.9.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1009 |
| 4   | input | Object | N   | 查询条件（可为空） |

#### 9.5.9.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 待确认工单列表 |

**data 待确认工单结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| workCode | String | 工单编码 |
| outboundOrderNumber | String | 出库单号 |
| createTime | String | 创建时间 |
| applicant | String | 申请人 |
| status | String | 工单状态 |

#### 9.5.9.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1009",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"workId": "PWO20250131-002",
"workCode": "PWO20250131-002",
"outboundOrderNumber": "OUT20250131002",
"createTime": "2025-01-31 10:00:00",
"applicant": "EMP001",
"status": "PENDING_CONFIRM"
}
\]
}

### 9.5.10 【M1010】获取可撤销工单列表接口

#### 9.5.10.1 接口说明

获取可以撤销的出库工单列表，用于查看哪些已完成的出库单据可以进行撤销操作。

#### 9.5.10.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 9.5.10.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：M1010 |
| 4   | input | Object | N   | 查询条件（可为空） |

#### 9.5.10.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | List | 可撤销工单列表 |

**data 可撤销工单结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| workId | String | 工单ID |
| workCode | String | 工单编码 |
| outboundOrderNumber | String | 出库单号 |
| completeTime | String | 完成时间 |
| applicant | String | 申请人 |
| status | String | 工单状态 |

#### 9.5.10.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "M1010",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"workId": "PWO20250131-001",
"workCode": "PWO20250131-001",
"outboundOrderNumber": "OUT20250131001",
"completeTime": "2025-01-31 14:00:00",
"applicant": "EMP001",
"status": "COMPLETED"
}
\]
}



## 第10章 PURMS采购管理系统接口

### 10.1 接口地址

#### 10.1.1 系统接口地址
- **外网地址**：https://hrp.zjxrmyy.cn:18090/back/purms/externalApi/gateway
- **内网地址**：https://10.2.233.11:8090/back/purms/externalApi/gateway

#### 10.1.2 接口调用说明
- **统一网关**：所有PURMS接口通过统一网关调用
- **接口区分**：通过请求参数中的`infno`字段区分具体接口（PUR1001-PUR1006）
- **认证方式**：hospitalId + encryptKey统一认证
- **加密秘钥**：encryptKey需要向我们申请获取

### 10.2 接口内容和列表

### 10.2.1 数据接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** |
| --- | --- | --- | --- | --- |
| PUR1001 | 获取二级库采购单详情接口 | HTTP POST | 获取所有与二级库物资相关的采购单明细信息 | 外部系统 |
| PUR1002 | 获取采购分类接口 | HTTP POST | 获取系统中所有采购分类信息 | 外部系统 |
| PUR1003 | 获取采购项目接口 | HTTP POST | 获取系统中所有采购项目信息 | 外部系统 |
| PUR1005 | 查询采购申请状态接口 | HTTP POST | 查询指定采购申请的审核状态 | 外部系统 |
| PUR1006 | 查询采购执行状态接口 | HTTP POST | 查询指定采购申请的执行状态 | 外部系统 |

### 10.2.2 服务接口列表

| **接口编号** | **接口名称** | **调用方式** | **描述** | **调用方** |
| --- | --- | --- | --- | --- |
| PUR1004 | 提交采购申请接口 | HTTP POST | 创建新的采购申请单据 | 外部系统 |

## 10.2 接口地址与URL映射配置

### 10.2.1 统一网关接口

| **接口名称** | **接口路径** | **完整URL** | **调用方** |
| --- | --- | --- | --- |
| 统一网关入口 | /gateway | http://ip:port/externalApi/gateway | 外部系统 |

### 10.2.2 认证信息

- **医院ID：** zjxrmyy
- **加密密钥：** 57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E

## 10.3 通用响应格式规范

### 10.3.1 成功响应格式

{
"code": 200,
"message": "成功",
"data": {
// 具体业务数据
}
}

### 10.3.2 分页响应格式

{
"code": 200,
"message": "成功",
"data": {
"list": \[
// 数据列表
\],
"total": 100,
"pageNum": 1,
"pageSize": 20,
"pages": 5
}
}

### 10.3.3 错误响应格式

{
"code": 400,
"message": "请求参数错误",
"data": null
}

## 10.4 业务流程说明

### 10.4.1 服务接口调用顺序

PURMS系统与**伟世数联后勤管理系统**的数据对接，主要提供采购签收单数据查询服务。

#### ******** PURMS与伟世数联系统对接流程

#### ******** 伟世数联系统数据获取时序图

**对接流程说明**：

1.  **伟世数联系统发起**：通过定时任务或手动触发方式调用PURMS接口
2.  **数据查询请求**：调用PUR1001接口，传入时间范围参数获取采购签收单数据
3.  **数据传输**：PURMS返回指定时间范围内所有已签收的采购单详情
4.  **入库处理**：伟世数联系统解析数据并执行库存入库操作

**核心价值**：

- **数据同步**：PURMS采购签收数据实时同步到伟世数联后勤管理系统
- **物资追溯**：通过物资唯一编码(tripartiteConsumableId)实现采购到库存的完整追溯
- **批量获取**：支持按时间范围批量获取签收单数据，提高同步效率
- **业务闭环**：完成从采购签收到库存入库的业务数据流转

**技术特点**：

- **统一网关**：所有接口调用通过PURMS统一网关进行认证和路由
- **核心接口**：PUR1001是唯一必需的核心接口，提供完整的签收单数据
- **简化对接**：专注于数据查询和传输，无需复杂的业务流程交互
- **数据完整**：返回采购申请ID、物资编码、金额、科室等关键业务数据

### 10.4.2 业务流程说明

1.  **伟世数联系统触发**：
    - 通过定时任务（如每日定时）或手动触发方式启动数据同步
    - 确定需要获取的时间范围（如昨日、本周、本月的采购签收数据）
2.  **核心数据获取**：
    - 调用 PUR1001 获取二级库采购单详情，这是**唯一核心接口**
    - 传入时间范围参数(startTime, endTime)进行批量查询
    - PURMS系统返回指定时间范围内所有已签收的采购单数据
    - 每条数据包含物资唯一编码、采购金额、申请科室等关键信息
3.  **伟世数联系统处理**：
    - 接收并解析PURMS返回的签收单数据
    - 根据物资唯一编码(tripartiteConsumableId)匹配伟世数联系统中的库存物资
    - 执行库存入库操作，更新库存数量和金额
    - 完成从PURMS采购签收到伟世数联库存管理的数据流转
4.  **数据验证与处理**：
    - 验证数据完整性和准确性
    - 处理可能的数据异常或重复
    - 记录同步日志，便于问题追踪和数据核对

## 10.5 详细接口规范

### 10.5.1 【PUR1001】获取二级库采购单详情接口

#### ******** 接口说明

获取所有与二级库物资（aset_type 以'03'开头）相关的采购单明细，每条明细包含采购单主信息和物资主数据。

#### ******** 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******** 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1001 |
| 4   | input | Object | N   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| startTime | String | N   | 开始时间，格式：YYYY-MM-DD HH:mm:ss |
| endTime | String | N   | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

#### 10.5.1.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Array | 采购单详情列表 |

**data 采购单详情结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| requestId | String | 采购申请ID |
| itemNo | String | 项目编号 |
| itemName | String | 采购项目名称 |
| purcType | String | 采购类型 |
| sumamt | BigDecimal | 总金额 |
| status | String | 采购状态 |
| tripartiteConsumableId | String | 物资唯一编码 |
| applyOrgName | String | 申请科室 |
| appyer | String | 申请人 |
| crteTime | String | 创建时间 |

#### ******** 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1001",
"input": {
"startTime": "2025-01-01 00:00:00",
"endTime": "2025-01-31 23:59:59"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"requestId": "REQ20250131001",
"itemNo": "ITEM001",
"itemName": "医用耗材采购项目",
"purcType": "3",
"sumamt": 50000.00,
"status": "1",
"tripartiteConsumableId": "MAT001",
"applyOrgName": "内科",
"appyer": "张医生",
"crteTime": "2025-01-31 10:00:00"
}
\]
}

### 10.5.2 【PUR1002】获取采购分类接口

#### ******** 接口说明

获取PURMS系统中所有采购分类信息，支持树形结构的分类数据查询。

#### ******** 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******** 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1002 |
| 4   | input | Object | N   | 无需额外参数 |

#### ******** 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Array | 采购分类列表 |

**data 采购分类结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| categoryId | String | 分类ID |
| code | String | 分类编码 |
| name | String | 分类名称 |
| parentId | String | 父级ID |
| isLeaf | Integer | 是否为叶子节点 (1: 是, 0: 否) |
| deleteFlag | Integer | 删除标志 (1: 已删除, 0: 未删除) |

#### 10.5.2.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1002",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"categoryId": "1",
"code": "1",
"name": "零星采购",
"parentId": null,
"isLeaf": 1,
"deleteFlag": 0
},
{
"categoryId": "2",
"code": "2",
"name": "大型设备采购",
"parentId": null,
"isLeaf": 1,
"deleteFlag": 0
},
{
"categoryId": "3",
"code": "3",
"name": "物资采购",
"parentId": null,
"isLeaf": 1,
"deleteFlag": 0
}
\]
}

### 10.5.3 【PUR1003】获取采购项目接口

#### 10.5.3.1 接口说明

获取PURMS系统中所有采购项目信息，包括项目的基本信息、预算金额、状态等详细数据。

#### 10.5.3.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 10.5.3.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1003 |
| 4   | input | Object | N   | 无需额外参数 |

#### 10.5.3.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Array | 采购项目列表 |

**data 采购项目结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| projectId | String | 项目ID |
| code | String | 项目编码 |
| name | String | 项目名称 |
| type | String | 项目类型 |
| budget | BigDecimal | 预算金额 |
| specification | String | 规格型号 |
| status | String | 项目状态 (1: 进行中, 2: 已完成, 3: 已取消) |
| deleteFlag | Integer | 删除标志 (1: 已删除, 0: 未删除) |
| categoryId | String | 分类ID |

#### 10.5.3.5 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1003",
"input": {}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": \[
{
"projectId": "PROJ001",
"code": "PROJ001",
"name": "医用耗材采购项目",
"type": "3",
"budget": 100000.00,
"specification": "一次性医用耗材",
"status": "1",
"deleteFlag": 0,
"categoryId": "3"
}
\]
}

### 10.5.4 【PUR1004】提交采购申请接口

#### 10.5.4.1 接口说明

创建新的采购申请单据，支持多种采购类型的申请提交。

#### 10.5.4.2 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### 10.5.4.3 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1004 |
| 4   | input | Object | Y   | 采购申请数据 |

**input 采购申请数据结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| purchaseName | String | Y   | 采购名称 |
| purchaseType | String | Y   | 采购类型 (1: 零星采购, 2: 大型设备采购, 3: 物资采购) |
| departmentId | String | Y   | 部门ID |
| applicant | String | Y   | 申请人 |
| description | String | N   | 描述  |
| purchaseItems | Array | Y   | 采购明细列表 |

**purchaseItems 采购明细项结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| productName | String | Y   | 产品名称 |
| specification | String | N   | 规格  |
| manufacturer | String | N   | 厂家  |
| unit | String | Y   | 单位  |
| quantity | Integer | Y   | 数量  |
| price | Double | N   | 单价  |
| totalPrice | Double | N   | 总价  |
| remark | String | N   | 备注  |

#### ******** 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 申请结果 |

**data 申请结果结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| requestId | String | 申请ID |
| status | String | 状态 (SUCCESS, FAILED) |
| message | String | 处理消息 |

#### ******** 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1004",
"input": {
"purchaseName": "医用耗材采购申请",
"purchaseType": "3",
"departmentId": "DEPT001",
"applicant": "张医生",
"description": "内科医用耗材采购",
"purchaseItems": \[
{
"productName": "一次性注射器",
"specification": "5ml",
"manufacturer": "某医疗器械公司",
"unit": "支",
"quantity": 1000,
"price": 1.5,
"totalPrice": 1500.0,
"remark": "急需"
}
\]
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"requestId": "REQ20250131002",
"status": "SUCCESS",
"message": "采购申请提交成功"
}
}

### 10.5.5 【PUR1005】查询采购申请状态接口

#### ******** 接口说明

查询指定采购申请的审核状态和详细信息。

#### ******** 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******** 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1005 |
| 4   | input | Object | Y   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| requestId | String | Y   | 申请ID |

#### 10.5.5.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 申请状态信息 |

**data 申请状态结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| requestId | String | 申请ID |
| requestName | String | 申请名称 |
| requestTime | String | 申请时间 |
| status | String | 申请状态 (0: 待审核, 1: 审核通过, 2: 审核拒绝, 3: 已取消) |
| statusDescription | String | 状态描述 |
| department | String | 申请部门 |
| applicant | String | 申请人 |
| totalAmount | BigDecimal | 总金额 |
| auditBatchNo | String | 审核批次号 |
| auditTime | String | 审核时间 |
| auditor | String | 审核人 |

#### ******** 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1005",
"input": {
"requestId": "REQ20250131002"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"requestId": "REQ20250131002",
"requestName": "医用耗材采购申请",
"requestTime": "2025-01-31 10:00:00",
"status": "1",
"statusDescription": "审核通过",
"department": "内科",
"applicant": "张医生",
"totalAmount": 1500.00,
"auditBatchNo": "AUDIT20250131001",
"auditTime": "2025-01-31 14:00:00",
"auditor": "审核员"
}
}

### 10.5.6 【PUR1006】查询采购执行状态接口

#### ******** 接口说明

查询指定采购申请的执行状态和执行详情。

#### ******** 接口路径

**请求方式**：POST

**接口路径**：/externalApi/gateway

#### ******** 输入参数

| **序号** | **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- | --- |
| 1   | hospitalId | String | Y   | 医院ID，固定值：zjxrmyy |
| 2   | encryptKey | String | Y   | 加密密钥 |
| 3   | infno | String | Y   | 接口编号，固定值：PUR1006 |
| 4   | input | Object | Y   | 查询条件 |

**input 查询条件结构**：

| **参数名称** | **参数类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| requestId | String | Y   | 申请ID |

#### 10.5.6.4 输出参数

| **序号** | **参数名称** | **参数类型** | **说明** |
| --- | --- | --- | --- |
| 1   | code | Integer | 响应状态码 |
| 2   | message | String | 响应消息 |
| 3   | data | Object | 执行状态信息 |

**data 执行状态结构**：

| **参数名称** | **参数类型** | **说明** |
| --- | --- | --- |
| requestId | String | 申请ID |
| requestName | String | 申请名称 |
| status | String | 执行状态 (0: 未执行, 1: 执行中, 2: 已完成, 3: 已取消) |
| statusDescription | String | 状态描述 |
| planAmount | BigDecimal | 计划金额 |
| actualAmount | BigDecimal | 实际金额 |
| executeTime | String | 执行时间 |
| executor | String | 执行人 |
| department | String | 执行部门 |

#### ******** 调用示例

**请求示例**：

{
"hospitalId": "zjxrmyy",
"encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
"infno": "PUR1006",
"input": {
"requestId": "REQ20250131002"
}
}

**响应示例**：

{
"code": 200,
"message": "成功",
"data": {
"requestId": "REQ20250131002",
"requestName": "医用耗材采购申请",
"status": "2",
"statusDescription": "已完成",
"planAmount": 1500.00,
"actualAmount": 1450.00,
"executeTime": "2025-01-31 16:00:00",
"executor": "采购员",
"department": "采购部"
}
}



---

## 附录A 统一错误码说明

### A.1 通用HTTP状态码

| **状态码** | **状态描述** | **说明** | **处理建议** |
| --- | --- | --- | --- |
| 200 | 成功 | 请求处理成功 | 继续处理返回数据 |
| 400 | 请求错误 | 请求参数错误或格式不正确 | 检查请求参数格式和必填字段 |
| 401 | 未授权 | 身份验证失败或token无效 | 重新登录获取有效token |
| 403 | 禁止访问 | 权限不足，无权限访问该资源 | 联系管理员分配相应权限 |
| 404 | 未找到 | 接口不存在或资源不存在 | 检查接口路径和资源ID |
| 500 | 服务器错误 | 内部服务器错误 | 联系技术支持人员 |

### A.2 HRM人力资源系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 1001 | 参数为空 | 必填参数缺失 | 检查必填参数 |
| 1002 | 参数格式错误 | 参数格式不正确 | 检查参数格式 |
| 1003 | 员工不存在 | 指定的员工ID不存在 | 检查员工ID是否正确 |
| 1004 | 组织不存在 | 指定的组织ID不存在 | 检查组织ID是否正确 |
| 1005 | 卫宁系统连接失败 | 无法连接到卫宁主数据系统 | 检查网络连接和卫宁系统状态 |

### A.3 BMS预算管理系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 2001 | 预算不存在 | 指定的预算ID不存在 | 检查预算ID是否正确 |
| 2002 | 预算已超支 | 预算执行金额超过预算额度 | 调整预算或减少支出 |
| 2003 | 部门权限不足 | 无权限查询该部门预算数据 | 联系管理员分配部门权限 |
| 2004 | 时间范围错误 | 查询时间范围超过限制 | 调整查询时间范围 |

### A.4 ECS费用报销系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 3001 | 报销单不存在 | 指定的报销单ID不存在 | 检查报销单ID是否正确 |
| 3002 | 文件上传失败 | 文件上传过程中出现错误 | 检查文件格式和大小 |
| 3003 | 数据同步失败 | 数据同步过程中出现错误 | 检查网络连接和目标系统 |
| 3004 | 服务调用超时 | 外部服务调用超时 | 重试或联系技术支持 |
| 3005 | 权限不足 | 用户权限不足 | 联系管理员分配权限 |
| 3006 | 数据不存在 | 查询的数据不存在 | 确认查询条件 |
| 3007 | 系统繁忙 | 系统当前繁忙，请稍后重试 | 稍后重试 |

### A.5 ERP财务核算系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 4001 | 凭证号已存在 | 生成的凭证号在系统中已存在 | 重新生成凭证号 |
| 4002 | 会计科目不存在 | 指定的会计科目代码不存在 | 检查会计科目代码 |
| 4003 | 借贷不平衡 | 凭证明细借方金额与贷方金额不相等 | 检查凭证明细金额 |
| 4004 | 凭证已生成 | 该报销单已生成凭证，不能重复生成 | 检查报销单状态 |
| 4005 | 用友系统连接超时 | 连接用友财务系统超时 | 检查网络连接 |
| 4006 | 用友系统认证失败 | 用友系统用户认证失败 | 检查用户名密码配置 |
| 4007 | 凭证推送失败 | 凭证数据推送到用友系统失败 | 检查凭证数据格式 |

### A.6 MMIS物资管理系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 5001 | 物资不存在 | 指定的物资ID在系统中不存在 | 检查物资ID是否正确 |
| 5002 | 库存不足 | 物资库存数量不足以满足出库需求 | 检查库存数量或调整出库数量 |
| 5003 | 工单不存在 | 指定的工单ID在系统中不存在 | 检查工单ID是否正确 |
| 5004 | 工单状态不允许操作 | 当前工单状态不允许执行该操作 | 检查工单状态 |
| 5005 | 出库数量错误 | 出库数量必须大于0 | 检查出库数量 |
| 5006 | 物资已锁定 | 物资已被其他操作锁定 | 等待锁定释放后重试 |

### A.7 PURMS采购管理系统错误码

| **错误码** | **错误信息** | **说明** | **解决方案** |
| --- | --- | --- | --- |
| 6001 | 采购分类不存在 | 指定的采购分类ID在系统中不存在 | 检查采购分类ID |
| 6002 | 采购项目不存在 | 指定的采购项目ID在系统中不存在 | 检查采购项目ID |
| 6003 | 申请ID不能为空 | 查询采购申请状态时申请ID为空 | 提供有效的申请ID |
| 6004 | 采购申请不存在 | 指定的采购申请ID在系统中不存在 | 检查采购申请ID |
| 6005 | 采购申请数据格式错误 | 提交的采购申请数据格式不符合要求 | 检查数据格式 |
| 6006 | 采购明细不能为空 | 采购申请中的明细列表为空 | 添加采购明细 |
| 6007 | 部门ID不存在 | 指定的部门ID在系统中不存在 | 检查部门ID |
| 6008 | 申请人不能为空 | 采购申请中的申请人字段为空 | 提供申请人信息 |

---

## 附录B 统一数据字典

### B.1 通用状态枚举

| **状态值** | **状态名称** | **说明** |
| --- | --- | --- |
| 0 | 未启用/停用 | 该记录处于停用状态 |
| 1 | 启用/正常 | 该记录处于启用状态 |

### B.2 删除标志枚举

| **标志值** | **说明** |
| --- | --- |
| 0 | 未删除/有效 |
| 1 | 已删除/无效 |

### B.3 审核状态枚举

| **状态值** | **状态名称** | **说明** |
| --- | --- | --- |
| 0 | 待审核 | 申请已提交，等待审核 |
| 1 | 审核通过 | 申请审核通过 |
| 2 | 审核拒绝 | 申请审核被拒绝 |
| 3 | 已取消 | 申请已被取消 |

### B.4 执行状态枚举

| **状态值** | **状态名称** | **说明** |
| --- | --- | --- |
| 0 | 未执行 | 任务尚未开始执行 |
| 1 | 执行中 | 任务正在执行中 |
| 2 | 已完成 | 任务执行完成 |
| 3 | 已取消 | 任务执行被取消 |

### B.5 时间格式规范

| **字段类型** | **格式** | **示例** |
| --- | --- | --- |
| 日期时间 | yyyy-MM-dd HH:mm:ss | 2025-01-31 10:00:00 |
| 日期 | yyyy-MM-dd | 2025-01-31 |
| 时间 | HH:mm:ss | 10:00:00 |
| 时间戳 | 毫秒级时间戳 | 1706688000000 |

### B.6 采购类型枚举

| **类型值** | **类型名称** | **说明** |
| --- | --- | --- |
| 1 | 零星采购 | 小额、零散的采购需求 |
| 2 | 大型设备采购 | 大型医疗设备的采购 |
| 3 | 物资采购 | 医用耗材等物资的采购 |

### B.7 费用类型枚举

| **类型代码** | **类型名称** | **说明** |
| --- | --- | --- |
| TRAVEL | 差旅费 | 出差相关费用报销 |
| OFFICE | 办公费 | 办公用品费用报销 |
| MEETING | 会议费 | 会议相关费用报销 |
| TRAINING | 培训费 | 培训相关费用报销 |
| OTHER | 其他费用 | 其他类型费用报销 |