/**
 * Electron-WebView 通信API统一入口
 * 提供完整的API接口和便捷方法
 */

// 导出类型定义
export * from './types'

// 导出通信核心
export * from './communication'

// 导出API接口
export * from './api'

// 导出WebView处理器
export * from './webview-handler'

// 导出便捷的组合API
import {
  ElectronAPI,
  ElectronAuth,
  ElectronProcess,
  ElectronNavigation,
  ElectronSystem,
} from './api'

import { electronCommunication } from './communication'
import { webViewHandler } from './webview-handler'

/**
 * 统一的Electron通信API
 */
export const ElectronBridge = {
  // 核心API
  api: ElectronAPI,
  
  // 便捷方法
  auth: ElectronAuth,
  process: ElectronProcess,
  navigation: ElectronNavigation,
  system: ElectronSystem,
  
  // 通信管理器
  communication: electronCommunication,
  
  // WebView处理器
  handler: webViewHandler,
  
  // 工具方法
  utils: {
    /**
     * 检查是否在Electron环境中
     */
    isElectron: () => electronCommunication.isElectron(),
    
    /**
     * 获取通信配置
     */
    getConfig: () => electronCommunication.getConfig(),
    
    /**
     * 更新通信配置
     */
    updateConfig: (config: any) => electronCommunication.updateConfig(config),
    
    /**
     * 清理所有资源
     */
    dispose: () => electronCommunication.dispose(),
  },
}

// 默认导出
export default ElectronBridge

// ==================== Vue组合式API ====================

import { ref, onUnmounted, computed } from 'vue'
import type { Ref } from 'vue'
import type {
  UserTokenInfo,
  SystemInfo,
  SystemStatusChangedEvent,
  ElectronEventUnsubscribe,
} from './types'

/**
 * 用户认证组合式API
 */
export function useElectronAuth() {
  const tokenInfo = ref<UserTokenInfo | null>(null)
  const isAuthenticated = computed(() => !!tokenInfo.value?.accessToken)
  const loading = ref(false)

  // 获取TOKEN
  const getToken = async () => {
    loading.value = true
    try {
      const result = await ElectronAuth.getToken()
      if (result.success && result.data?.tokenInfo) {
        tokenInfo.value = result.data.tokenInfo
      }
      return result
    } finally {
      loading.value = false
    }
  }

  // 设置TOKEN
  const setToken = async (token: UserTokenInfo) => {
    loading.value = true
    try {
      const result = await ElectronAuth.setToken(token)
      if (result.success) {
        tokenInfo.value = token
      }
      return result
    } finally {
      loading.value = false
    }
  }

  // 清除TOKEN
  const clearToken = async () => {
    loading.value = true
    try {
      const result = await ElectronAuth.clearToken()
      if (result.success) {
        tokenInfo.value = null
      }
      return result
    } finally {
      loading.value = false
    }
  }

  // 检查TOKEN有效性
  const checkTokenValidity = async () => {
    return await ElectronAuth.isTokenValid()
  }

  // 监听TOKEN过期
  let unsubscribeTokenExpired: ElectronEventUnsubscribe | null = null
  const onTokenExpired = (callback: () => void) => {
    unsubscribeTokenExpired = ElectronAuth.onTokenExpired(() => {
      tokenInfo.value = null
      callback()
    })
  }

  // 清理
  onUnmounted(() => {
    if (unsubscribeTokenExpired) {
      unsubscribeTokenExpired()
    }
  })

  return {
    tokenInfo: tokenInfo as Ref<UserTokenInfo | null>,
    isAuthenticated,
    loading: loading as Ref<boolean>,
    getToken,
    setToken,
    clearToken,
    checkTokenValidity,
    onTokenExpired,
  }
}

/**
 * 系统状态组合式API
 */
export function useElectronSystem() {
  const systems = ref<SystemInfo[]>([])
  const warnNums = ref<Record<number, number>>({})
  const loading = ref(false)

  // 获取系统列表
  const getSystemList = async () => {
    loading.value = true
    try {
      const result = await ElectronSystem.getList()
      if (result.success && result.data?.systems) {
        systems.value = result.data.systems
      }
      return result
    } finally {
      loading.value = false
    }
  }

  // 获取警告数量
  const getWarnNum = async (systemIds?: number[]) => {
    const result = await ElectronSystem.getWarnNum(systemIds)
    if (result.success && result.data?.warnNums) {
      warnNums.value = { ...warnNums.value, ...result.data.warnNums }
    }
    return result
  }

  // 订阅状态变化
  let unsubscribeStatusFn: ElectronEventUnsubscribe | null = null
  const subscribeStatus = (callback?: (event: SystemStatusChangedEvent) => void) => {
    unsubscribeStatusFn = ElectronSystem.subscribe((event) => {
      // 更新本地状态
      if (event.changeType === 'warn_num_changed') {
        warnNums.value[event.systemId] = event.warnNum
      }

      // 调用用户回调
      if (callback) {
        callback(event)
      }
    })
  }

  // 取消订阅
  const unsubscribeStatus = async () => {
    if (unsubscribeStatusFn) {
      unsubscribeStatusFn()
      unsubscribeStatusFn = null
    }
    return await ElectronSystem.unsubscribe()
  }

  // 清理
  onUnmounted(() => {
    if (unsubscribeStatusFn) {
      unsubscribeStatusFn()
    }
  })

  return {
    systems: systems as Ref<SystemInfo[]>,
    warnNums: warnNums as Ref<Record<number, number>>,
    loading: loading as Ref<boolean>,
    getSystemList,
    getWarnNum,
    subscribeStatus,
    unsubscribeStatus,
  }
}

/**
 * 导航组合式API
 */
export function useElectronNavigation() {
  const loading = ref(false)

  // 跳转到模块
  const toModule = async (systemId: number, options?: { onlyLoad?: boolean; callbackPath?: string }) => {
    loading.value = true
    try {
      return await ElectronNavigation.toModule(systemId, options)
    } finally {
      loading.value = false
    }
  }

  // 跳转到菜单
  const toMenu = async (
    systemId: number,
    menuPath: string,
    options?: { menuParams?: Record<string, any>; openInNewWindow?: boolean }
  ) => {
    loading.value = true
    try {
      return await ElectronNavigation.toMenu(systemId, menuPath, options)
    } finally {
      loading.value = false
    }
  }

  // 加载模块
  const loadModule = async (systemId: number) => {
    loading.value = true
    try {
      return await ElectronNavigation.loadModule(systemId)
    } finally {
      loading.value = false
    }
  }

  return {
    loading: loading as Ref<boolean>,
    toModule,
    toMenu,
    loadModule,
  }
}

/**
 * 流程组合式API
 */
export function useElectronProcess() {
  const loading = ref(false)

  // 打开流程详情
  const openDetail = async (
    processInstanceId: string,
    options?: {
      openInNewWindow?: boolean
      windowOptions?: { width?: number; height?: number; modal?: boolean }
    }
  ) => {
    loading.value = true
    try {
      return await ElectronProcess.openDetail(processInstanceId, options)
    } finally {
      loading.value = false
    }
  }

  // 在新窗口打开
  const openInNewWindow = async (
    processInstanceId: string,
    windowOptions?: { width?: number; height?: number }
  ) => {
    return await ElectronProcess.openDetailInNewWindow(processInstanceId, windowOptions)
  }

  return {
    loading: loading as Ref<boolean>,
    openDetail,
    openInNewWindow,
  }
}
